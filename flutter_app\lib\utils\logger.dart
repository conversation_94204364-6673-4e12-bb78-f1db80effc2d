import 'dart:developer' as developer;

/// 日志工具类
class Logger {
  static const String _tag = 'ReportGenerator';
  
  /// 调试日志
  static void debug(String message, [Object? error, StackTrace? stackTrace]) {
    developer.log(
      message,
      name: _tag,
      level: 500, // DEBUG
      error: error,
      stackTrace: stackTrace,
    );
  }
  
  /// 信息日志
  static void info(String message, [Object? error, StackTrace? stackTrace]) {
    developer.log(
      message,
      name: _tag,
      level: 800, // INFO
      error: error,
      stackTrace: stackTrace,
    );
  }
  
  /// 警告日志
  static void warning(String message, [Object? error, StackTrace? stackTrace]) {
    developer.log(
      message,
      name: _tag,
      level: 900, // WARNING
      error: error,
      stackTrace: stackTrace,
    );
  }
  
  /// 错误日志
  static void error(String message, [Object? error, StackTrace? stackTrace]) {
    developer.log(
      message,
      name: _tag,
      level: 1000, // ERROR
      error: error,
      stackTrace: stackTrace,
    );
  }
  
  /// 严重错误日志
  static void severe(String message, [Object? error, StackTrace? stackTrace]) {
    developer.log(
      message,
      name: _tag,
      level: 1200, // SEVERE
      error: error,
      stackTrace: stackTrace,
    );
  }
}

/// 错误处理工具类
class ErrorHandler {
  /// 处理并格式化错误信息
  static String formatError(dynamic error) {
    if (error == null) return '未知错误';
    
    String errorMessage = error.toString();
    
    // 网络错误处理
    if (errorMessage.contains('SocketException')) {
      return '网络连接失败，请检查网络设置';
    }
    
    if (errorMessage.contains('TimeoutException')) {
      return '请求超时，请稍后重试';
    }
    
    if (errorMessage.contains('FormatException')) {
      return '数据格式错误';
    }
    
    // HTTP错误处理
    if (errorMessage.contains('400')) {
      return '请求参数错误';
    }
    
    if (errorMessage.contains('401')) {
      return 'API密钥无效或已过期';
    }
    
    if (errorMessage.contains('403')) {
      return '访问被拒绝，请检查权限';
    }
    
    if (errorMessage.contains('404')) {
      return '请求的资源不存在';
    }
    
    if (errorMessage.contains('429')) {
      return 'API调用频率过高，请稍后重试';
    }
    
    if (errorMessage.contains('500')) {
      return '服务器内部错误';
    }
    
    if (errorMessage.contains('502') || errorMessage.contains('503')) {
      return '服务暂时不可用，请稍后重试';
    }
    
    // 数据库错误处理
    if (errorMessage.contains('DatabaseException')) {
      return '数据库操作失败';
    }
    
    // 文件操作错误处理
    if (errorMessage.contains('FileSystemException')) {
      return '文件操作失败，请检查存储权限';
    }
    
    // 权限错误处理
    if (errorMessage.contains('Permission')) {
      return '权限不足，请在设置中授予相应权限';
    }
    
    // 返回原始错误信息（去掉技术细节）
    if (errorMessage.length > 100) {
      return errorMessage.substring(0, 100) + '...';
    }
    
    return errorMessage;
  }
  
  /// 记录错误并返回用户友好的错误信息
  static String handleError(String context, dynamic error, [StackTrace? stackTrace]) {
    final formattedError = formatError(error);
    Logger.error('$context: $formattedError', error, stackTrace);
    return formattedError;
  }
}
