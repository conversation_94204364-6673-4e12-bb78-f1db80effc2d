import 'package:flutter/foundation.dart';
import '../models/report_table.dart';
import '../services/database_service.dart';
import '../services/ai_service.dart';

/// 报表数据状态管理
class ReportProvider extends ChangeNotifier {
  final DatabaseService _databaseService;
  final AIService _aiService = AIService();

  // 当前状态
  bool _isLoading = false;
  String? _errorMessage;
  
  // 数据
  List<ReportTable> _historyReports = [];
  ReportTable? _currentReport;
  int? _selectedHistoryIndex;

  // 构造函数
  ReportProvider(this._databaseService) {
    _loadHistoryReports();
  }

  // Getters
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  List<ReportTable> get historyReports => _historyReports;
  ReportTable? get currentReport => _currentReport;
  int? get selectedHistoryIndex => _selectedHistoryIndex;

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// 清除错误信息
  void clearError() {
    _setError(null);
  }

  /// 加载历史报表列表
  Future<void> _loadHistoryReports() async {
    try {
      _historyReports = await _databaseService.getAllReports();
      notifyListeners();
    } catch (e) {
      _setError('加载历史报表失败: $e');
    }
  }

  /// 刷新历史报表列表
  Future<void> refreshHistoryReports() async {
    await _loadHistoryReports();
  }

  /// 使用AI解析文本生成报表
  Future<void> generateReportFromText(String text) async {
    if (text.trim().isEmpty) {
      _setError('请输入项目汇报文本');
      return;
    }

    _setLoading(true);
    _setError(null);

    try {
      final report = await _aiService.parseTextToTable(text);
      if (report != null) {
        _currentReport = report;
        _selectedHistoryIndex = null; // 清除历史选择
        notifyListeners();
      } else {
        _setError('AI未返回有效的表格数据');
      }
    } catch (e) {
      _setError('生成报表失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 保存当前报表
  Future<void> saveCurrentReport({String? customTitle}) async {
    if (_currentReport == null) {
      _setError('没有可保存的报表');
      return;
    }

    try {
      final title = customTitle ?? _currentReport!.title;
      final reportToSave = _currentReport!.copyWith(title: title);
      
      await _databaseService.saveReport(reportToSave);
      await _loadHistoryReports(); // 刷新历史列表
      _setError(null);
    } catch (e) {
      _setError('保存报表失败: $e');
    }
  }

  /// 选择历史报表
  Future<void> selectHistoryReport(int index) async {
    if (index < 0 || index >= _historyReports.length) {
      _setError('无效的报表索引');
      return;
    }

    try {
      _selectedHistoryIndex = index;
      _currentReport = _historyReports[index];
      _setError(null);
      notifyListeners();
    } catch (e) {
      _setError('加载报表失败: $e');
    }
  }

  /// 删除历史报表
  Future<void> deleteHistoryReport(int index) async {
    if (index < 0 || index >= _historyReports.length) {
      _setError('无效的报表索引');
      return;
    }

    try {
      final report = _historyReports[index];
      if (report.id != null) {
        await _databaseService.deleteReport(report.id!);
        await _loadHistoryReports(); // 刷新历史列表
        
        // 如果删除的是当前选中的报表，清除当前报表
        if (_selectedHistoryIndex == index) {
          _currentReport = null;
          _selectedHistoryIndex = null;
        } else if (_selectedHistoryIndex != null && _selectedHistoryIndex! > index) {
          _selectedHistoryIndex = _selectedHistoryIndex! - 1;
        }
        
        notifyListeners();
      }
    } catch (e) {
      _setError('删除报表失败: $e');
    }
  }

  /// 创建新的空报表
  void createNewReport() {
    _currentReport = ReportTable(
      title: '新建报表_${DateTime.now().toString().substring(0, 19)}',
      headers: const ["项目名称", "融资额 (亿元)", "项目状态", "核心进展", "下一步工作计划", "难点与风险", "最近更新日期"],
      data: [[]],
      createdAt: DateTime.now(),
    );
    _selectedHistoryIndex = null;
    _setError(null);
    notifyListeners();
  }

  /// 更新当前报表的单元格数据
  void updateCellData(int row, int column, String value) {
    if (_currentReport == null) return;
    
    final data = List<List<String>>.from(_currentReport!.data);
    
    // 确保行存在
    while (data.length <= row) {
      data.add(List.filled(_currentReport!.headers.length, ''));
    }
    
    // 确保列存在
    if (data[row].length <= column) {
      data[row] = List.from(data[row])..length = _currentReport!.headers.length;
      for (int i = 0; i < data[row].length; i++) {
        data[row][i] ??= '';
      }
    }
    
    data[row][column] = value;
    _currentReport = _currentReport!.copyWith(data: data);
    notifyListeners();
  }

  /// 添加新行
  void addRow() {
    if (_currentReport == null) return;
    
    final data = List<List<String>>.from(_currentReport!.data);
    data.add(List.filled(_currentReport!.headers.length, ''));
    _currentReport = _currentReport!.copyWith(data: data);
    notifyListeners();
  }

  /// 删除行
  void deleteRow(int index) {
    if (_currentReport == null || index < 0 || index >= _currentReport!.data.length) return;
    
    final data = List<List<String>>.from(_currentReport!.data);
    data.removeAt(index);
    _currentReport = _currentReport!.copyWith(data: data);
    notifyListeners();
  }

  /// 添加新列
  void addColumn(String headerName) {
    if (_currentReport == null) return;
    
    final headers = List<String>.from(_currentReport!.headers);
    headers.add(headerName);
    
    final data = _currentReport!.data.map((row) {
      final newRow = List<String>.from(row);
      newRow.add('');
      return newRow;
    }).toList();
    
    _currentReport = _currentReport!.copyWith(headers: headers, data: data);
    notifyListeners();
  }

  /// 删除列
  void deleteColumn(int index) {
    if (_currentReport == null || index < 0 || index >= _currentReport!.headers.length) return;
    if (_currentReport!.headers.length <= 1) return; // 至少保留一列
    
    final headers = List<String>.from(_currentReport!.headers);
    headers.removeAt(index);
    
    final data = _currentReport!.data.map((row) {
      final newRow = List<String>.from(row);
      if (newRow.length > index) {
        newRow.removeAt(index);
      }
      return newRow;
    }).toList();
    
    _currentReport = _currentReport!.copyWith(headers: headers, data: data);
    notifyListeners();
  }

  /// 更新表头
  void updateHeader(int index, String newName) {
    if (_currentReport == null || index < 0 || index >= _currentReport!.headers.length) return;
    
    final headers = List<String>.from(_currentReport!.headers);
    headers[index] = newName;
    _currentReport = _currentReport!.copyWith(headers: headers);
    notifyListeners();
  }

  @override
  void dispose() {
    _aiService.dispose();
    super.dispose();
  }
}
