# ==============================================================================
# 1. 导入所有必要的库
# ==============================================================================
import tkinter as tk
from tkinter import ttk, simpledialog, messagebox, filedialog
import sqlite3
import json
import openai # 或者 import requests
from PIL import Image, ImageDraw, ImageFont
from datetime import datetime
import threading
import time

# ==============================================================================
# 2. 全局配置与常量
# ==============================================================================
# 请在使用前填写您的API Key
API_KEY = "sk-c5I0FtKg8D8VScXOEWJBDja4e8ibiJkxsLI3IvGM9B6GrLmZ" 
API_BASE_URL = "https://api.chatanywhere.tech/v1/chat/completions" # 如果使用兼容API，请修改此地址
model = "gpt-4.1"

DB_FILE = "project_reports.db"
DEFAULT_HEADERS = ["项目名称", "融资额 (亿元)", "项目状态", "核心进展", "下一步工作计划", "难点与风险", "最近更新日期"]
WINDOW_TITLE = "一键报表生成器 v1.0"
WINDOW_GEOMETRY = "1200x800"


# ==============================================================================
# 3. 主应用程序类 (ReportApp)
# ==============================================================================
class ReportApp:
    def __init__(self, root):
        """类的构造函数，程序启动时执行"""
        self.root = root
        self.root.title(WINDOW_TITLE)
        self.root.geometry(WINDOW_GEOMETRY)

        # 初始化数据库连接
        self.db_conn = self._setup_database()

        # 创建UI界面
        self._create_widgets()

        # 启动时加载历史记录
        self._load_history_list()

    # --------------------------------------------------------------------------
    # UI创建与布局 (所有tk控件的定义)
    # --------------------------------------------------------------------------
    def _create_widgets(self):
        """创建所有界面组件"""
        # 主Frame，分为左侧历史区和右侧编辑区
        self.main_frame = tk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧历史区
        self.left_frame = tk.Frame(self.main_frame, width=250)
        self.left_frame.pack(side=tk.LEFT, fill=tk.Y)
        self.left_frame.pack_propagate(False)
        tk.Label(self.left_frame, text="历史报表", font=("微软雅黑", 12, "bold")).pack(pady=5)
        self.history_listbox = tk.Listbox(self.left_frame)
        self.history_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.history_listbox.bind("<<ListboxSelect>>", self._on_history_select)
        self.delete_history_btn = tk.Button(self.left_frame, text="删除选中报表", command=self._on_delete_history)
        self.delete_history_btn.pack(pady=5)

        # 右侧编辑区
        self.right_frame = tk.Frame(self.main_frame)
        self.right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 顶部输入区
        self.input_frame = tk.Frame(self.right_frame)
        self.input_frame.pack(fill=tk.X, pady=5)
        tk.Label(self.input_frame, text="粘贴项目汇报文本:", font=("微软雅黑", 11)).pack(side=tk.LEFT, padx=5)
        self.input_text = tk.Text(self.input_frame, height=4, font=("微软雅黑", 10))
        self.input_text.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.generate_button = tk.Button(self.input_frame, text="一键生成表格", command=self._on_generate_click)
        self.generate_button.pack(side=tk.LEFT, padx=5)

        # 表格区
        self.table_frame = tk.Frame(self.right_frame)
        self.table_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        self._init_table(DEFAULT_HEADERS)

        # 底部操作区
        self.bottom_frame = tk.Frame(self.right_frame)
        self.bottom_frame.pack(fill=tk.X, pady=5)
        self.add_row_btn = tk.Button(self.bottom_frame, text="添加行", command=self._on_add_row)
        self.add_row_btn.pack(side=tk.LEFT, padx=5)
        self.del_row_btn = tk.Button(self.bottom_frame, text="删除选中行", command=self._on_delete_row)
        self.del_row_btn.pack(side=tk.LEFT, padx=5)
        self.add_col_btn = tk.Button(self.bottom_frame, text="添加列", command=self._on_add_col)
        self.add_col_btn.pack(side=tk.LEFT, padx=5)
        self.del_col_btn = tk.Button(self.bottom_frame, text="删除选中列", command=self._on_delete_col)
        self.del_col_btn.pack(side=tk.LEFT, padx=5)
        self.export_img_btn = tk.Button(self.bottom_frame, text="导出为图片", command=self._on_export_image_click)
        self.export_img_btn.pack(side=tk.RIGHT, padx=5)
        print("UI界面创建完成")

    def _init_table(self, headers):
        """初始化/重建表格控件"""
        # 若已存在旧表格，先销毁
        if hasattr(self, 'table'):
            self.table.destroy()
        self.table = ttk.Treeview(self.table_frame, columns=headers, show='headings', selectmode='extended')
        for col in headers:
            self.table.heading(col, text=col, command=lambda c=col: self._on_header_click(c))
            self.table.column(col, width=120, anchor='center')
        self.table.pack(fill=tk.BOTH, expand=True)
        self.table.bind('<Double-1>', self._on_cell_double_click)
        self.table.bind('<Button-3>', self._on_table_right_click)
        self.table_headers = headers.copy()
        self._table_popup_menu = None

    # ===================== 表格编辑相关 =====================
    def _on_cell_double_click(self, event):
        """双击单元格进入编辑模式"""
        region = self.table.identify('region', event.x, event.y)
        if region != 'cell':
            return
        row_id = self.table.identify_row(event.y)
        col = self.table.identify_column(event.x)
        col_idx = int(col.replace('#', '')) - 1
        x, y, width, height = self.table.bbox(row_id, col)
        value = self.table.set(row_id, self.table_headers[col_idx])
        self._edit_var = tk.StringVar(value=value)
        self._edit_entry = tk.Entry(self.table, textvariable=self._edit_var)
        self._edit_entry.place(x=x, y=y, width=width, height=height)
        self._edit_entry.focus()
        self._edit_entry.bind('<Return>', lambda e: self._save_cell_edit(row_id, col_idx))
        self._edit_entry.bind('<FocusOut>', lambda e: self._save_cell_edit(row_id, col_idx))

    def _save_cell_edit(self, row_id, col_idx):
        """保存单元格编辑内容"""
        new_val = self._edit_var.get()
        self.table.set(row_id, self.table_headers[col_idx], new_val)
        self._edit_entry.destroy()

    def _on_add_row(self):
        """添加新行"""
        values = ["" for _ in self.table_headers]
        self.table.insert('', 'end', values=values)

    def _on_delete_row(self):
        """删除选中行"""
        selected = self.table.selection()
        for item in selected:
            self.table.delete(item)

    def _on_add_col(self):
        """添加新列"""
        col_name = simpledialog.askstring("添加列", "请输入新列名：", parent=self.root)
        if not col_name:
            return
        self.table_headers.append(col_name)
        self._init_table(self.table_headers)
        # 重新填充原有数据
        for row in self._get_table_data():
            self.table.insert('', 'end', values=row + [""])

    def _on_delete_col(self):
        """删除选中列"""
        if len(self.table_headers) <= 1:
            messagebox.showwarning("警告", "至少保留一列！")
            return
        col_idx = self._select_column_dialog()
        if col_idx is None:
            return
        del self.table_headers[col_idx]
        self._init_table(self.table_headers)
        # 重新填充原有数据
        for row in self._get_table_data():
            new_row = row[:col_idx] + row[col_idx+1:]
            self.table.insert('', 'end', values=new_row)

    def _select_column_dialog(self):
        """弹窗选择要删除的列，返回列索引"""
        win = tk.Toplevel(self.root)
        win.title("选择要删除的列")
        var = tk.IntVar(value=0)
        for i, h in enumerate(self.table_headers):
            tk.Radiobutton(win, text=h, variable=var, value=i).pack(anchor='w')
        tk.Button(win, text="确定", command=win.destroy).pack()
        win.wait_window()
        return var.get() if 0 <= var.get() < len(self.table_headers) else None

    def _on_header_click(self, col):
        """点击表头进行编辑"""
        idx = self.table_headers.index(col)
        new_name = simpledialog.askstring("编辑表头", f"请输入新的表头名称（原：{col}）：", parent=self.root)
        if new_name and new_name != col:
            if messagebox.askyesno("联动更新", "是否将所有历史表头一并更新？"):
                self._update_all_headers(new_name, idx)
            self.table_headers[idx] = new_name
            self._init_table(self.table_headers)
            for row in self._get_table_data():
                self.table.insert('', 'end', values=row)

    def _update_all_headers(self, new_name, idx):
        """更新数据库所有表头（简单实现：只更新默认表头配置）"""
        global DEFAULT_HEADERS
        DEFAULT_HEADERS[idx] = new_name

    def _on_table_right_click(self, event):
        """右键菜单：增删行列"""
        if self._table_popup_menu:
            self._table_popup_menu.destroy()
        menu = tk.Menu(self.table, tearoff=0)
        menu.add_command(label="添加行", command=self._on_add_row)
        menu.add_command(label="删除选中行", command=self._on_delete_row)
        menu.add_separator()
        menu.add_command(label="添加列", command=self._on_add_col)
        menu.add_command(label="删除选中列", command=self._on_delete_col)
        menu.tk_popup(event.x_root, event.y_root)
        self._table_popup_menu = menu

    def _get_table_data(self):
        """获取当前表格所有数据（二维列表）"""
        data = []
        for row_id in self.table.get_children():
            row = [self.table.set(row_id, h) for h in self.table_headers]
            data.append(row)
        return data

    # --------------------------------------------------------------------------
    # 数据库相关方法
    # --------------------------------------------------------------------------
    def _setup_database(self):
        """初始化数据库，如果表不存在则创建"""
        conn = sqlite3.connect(DB_FILE)
        c = conn.cursor()
        c.execute('''CREATE TABLE IF NOT EXISTS saved_tables (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT,
            headers TEXT,
            data TEXT,
            created_at TEXT
        )''')
        conn.commit()
        print("数据库连接并设置完成")
        return conn

    def _load_history_list(self):
        """从数据库加载历史报表标题到左侧列表"""
        self.history_listbox.delete(0, tk.END)
        c = self.db_conn.cursor()
        c.execute("SELECT id, title FROM saved_tables ORDER BY created_at DESC")
        self._history_id_map = []
        for row in c.fetchall():
            self.history_listbox.insert(tk.END, row[1])
            self._history_id_map.append(row[0])

    def _save_current_table(self, table_data=None):
        """将当前生成的表格数据存入数据库"""
        if table_data is None:
            table_data = self._get_table_data()
        title = simpledialog.askstring("保存报表", "请输入报表标题：", parent=self.root)
        if not title:
            return
        headers = json.dumps(self.table_headers, ensure_ascii=False)
        data = json.dumps(table_data, ensure_ascii=False)
        created_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        c = self.db_conn.cursor()
        c.execute("INSERT INTO saved_tables (title, headers, data, created_at) VALUES (?, ?, ?, ?)",
                  (title, headers, data, created_at))
        self.db_conn.commit()
        self._load_history_list()
        messagebox.showinfo("提示", "表格已保存！")

    def _on_history_select(self, event):
        """选中历史报表，加载表格"""
        idx = self.history_listbox.curselection()
        if not idx:
            return
        table_id = self._history_id_map[idx[0]]
        c = self.db_conn.cursor()
        c.execute("SELECT headers, data FROM saved_tables WHERE id=?", (table_id,))
        row = c.fetchone()
        if row:
            headers = json.loads(row[0])
            data = json.loads(row[1])
            self._init_table(headers)
            for r in data:
                self.table.insert('', 'end', values=r)
            self.table_headers = headers.copy()

    def _on_delete_history(self):
        """删除选中历史报表"""
        idx = self.history_listbox.curselection()
        if not idx:
            return
        table_id = self._history_id_map[idx[0]]
        if messagebox.askyesno("确认删除", "确定要删除该报表吗？"):
            c = self.db_conn.cursor()
            c.execute("DELETE FROM saved_tables WHERE id=?", (table_id,))
            self.db_conn.commit()
            self._load_history_list()
            messagebox.showinfo("提示", "已删除！")

    # --------------------------------------------------------------------------
    # 核心功能与事件处理方法 (按钮点击等事件的响应函数)
    # --------------------------------------------------------------------------
    def _on_generate_click(self):
        """“一键生成”按钮的点击事件处理"""
        text = self.input_text.get("1.0", tk.END).strip()
        if not text:
            messagebox.showwarning("提示", "请输入项目汇报文本！")
            return
        self.generate_button.config(state=tk.DISABLED)
        self.root.config(cursor="wait")
        threading.Thread(target=self._generate_table_thread, args=(text,)).start()

    def _generate_table_thread(self, report_text):
        """在后台线程中执行AI调用和处理"""
        try:
            table_data = self._call_ai_model(report_text)
            if not table_data:
                self._show_message("AI未返回有效表格数据！")
                return
            self.root.after(0, self._update_table_from_ai, table_data)
        finally:
            self.root.after(0, lambda: self.generate_button.config(state=tk.NORMAL))
            self.root.after(0, lambda: self.root.config(cursor=""))

    def _update_table_from_ai(self, table_data):
        """用AI返回的数据更新表格"""
        if not table_data:
            return
        headers = list(table_data[0].keys())
        self._init_table(headers)
        for row in table_data:
            self.table.insert('', 'end', values=[row.get(h, "") for h in headers])
        self.table_headers = headers.copy()
        if messagebox.askyesno("保存表格", "是否保存本次生成的表格？"):
            self._save_current_table(table_data=[[row.get(h, "") for h in headers] for row in table_data])

    def _call_ai_model(self, text):
        """构建Prompt并调用大模型API，包含重试逻辑"""
        prompt = f"请将以下项目汇报文本智能解析为标准表格，每个项目一行，字段包括：{', '.join(DEFAULT_HEADERS)}。输出JSON数组，每行一个项目。\n\n{text}"
        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Content-Type": "application/json"
        }
        payload = {
            "model": model,
            "messages": [
                {"role": "system", "content": "你是一个擅长结构化信息提取的AI助手。"},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.2
        }
        for _ in range(3):
            try:
                import requests
                resp = requests.post(f"{API_BASE_URL}/chat/completions", headers=headers, json=payload, timeout=30)
                if resp.status_code == 200:
                    content = resp.json()["choices"][0]["message"]["content"]
                    # 尝试提取JSON
                    import re
                    match = re.search(r'\[.*\]', content, re.DOTALL)
                    json_str = match.group(0) if match else content
                    data = json.loads(json_str)
                    if isinstance(data, list):
                        return data
                else:
                    print("API错误：", resp.text)
            except Exception as e:
                print("API调用异常：", e)
                time.sleep(1)
        return None

    def _show_message(self, msg):
        messagebox.showinfo("提示", msg)

    # ===================== 导出图片相关 =====================
    def _on_export_image_click(self):
        """“导出为图片”按钮的点击事件处理"""
        file_path = filedialog.asksaveasfilename(defaultextension='.png', filetypes=[('PNG图片', '*.png'), ('JPG图片', '*.jpg')])
        if not file_path:
            return
        table_data = self._get_table_data()
        img = self._create_table_image(table_data, self.table_headers)
        try:
            img.save(file_path)
            messagebox.showinfo("导出成功", f"图片已保存到：{file_path}")
        except Exception as e:
            messagebox.showerror("保存失败", str(e))

    def _create_table_image(self, table_data, headers):
        """使用Pillow库绘制表格图片，实现动态行高和列宽"""
        # 字体设置
        font_path = "msyh.ttc"  # 微软雅黑，需保证本地有此字体
        try:
            font = ImageFont.truetype(font_path, 32)
        except:
            font = ImageFont.load_default()
        padding = 20
        col_widths = []
        # 计算每列最大宽度
        for i, h in enumerate(headers):
            max_len = max([len(str(r[i])) for r in table_data] + [len(h)])
            col_widths.append(max(120, min(400, max_len * 32)))
        total_width = sum(col_widths) + padding * 2
        row_height = 60
        img_height = (len(table_data) + 1) * row_height + padding * 2
        img = Image.new('RGB', (total_width, img_height), color='white')
        draw = ImageDraw.Draw(img)
        # 绘制表头
        x = padding
        y = padding
        for i, h in enumerate(headers):
            draw.rectangle([x, y, x+col_widths[i], y+row_height], fill='#e0e0e0')
            draw.text((x+10, y+10), h, font=font, fill='black')
            x += col_widths[i]
        # 绘制表格内容
        y += row_height
        for row in table_data:
            x = padding
            for i, cell in enumerate(row):
                draw.rectangle([x, y, x+col_widths[i], y+row_height], outline='#888')
                draw.text((x+10, y+10), str(cell), font=font, fill='black')
                x += col_widths[i]
            y += row_height
        return img

    def run(self):
        """启动Tkinter主事件循环"""
        self.root.mainloop()

# ==============================================================================
# 4. 程序主入口
# ==============================================================================
if __name__ == "__main__":
    # 实例化主窗口
    root = tk.Tk()
    # 实例化应用类
    app = ReportApp(root)
    # 启动应用
    app.run()
    test_text = '''
    1. 汈汊湖项目（融资额5亿）
    ✅ 核心进展： 完成住建、自规、水泊局、发改等多部门批复。
    ➡ 下一步工作：继续协助其他手续办理。

    2. 绿美乡村项目（融资额2.6亿）
    ✅核心进展：调整版融资方案已经与银行、国投沟通，待确定。
    ➡ 下一步工作：现场继续踏勘，确保苗木绿化种植需求。 与国投确认林权证办理事宜。
    ‼ 难点：林地需要办理林权证。

    3. 城区供水提质增效项目（融资额9.6亿）
    4. 乡镇供水巩固提项目（融资额9亿）
    ✅核心进展：项目方案确定中。
    ➡ 下一步工作：编制项目建议书。

    5. 全域交通配套项目（融资额4亿）
    ✅核心进展：可研编制中，目前因投资受限，或需调整贷款额。
        
    6. 集贸片区改造项目（融资额4亿）
    ✅核心进展：方案编制中。
    '''