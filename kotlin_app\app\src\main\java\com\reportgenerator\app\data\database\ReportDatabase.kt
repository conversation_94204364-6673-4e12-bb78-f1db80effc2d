package com.reportgenerator.app.data.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.reportgenerator.app.data.dao.ReportTableDao
import com.reportgenerator.app.data.model.Converters
import com.reportgenerator.app.data.model.ReportTable

/**
 * Room 数据库
 */
@Database(
    entities = [ReportTable::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class ReportDatabase : RoomDatabase() {
    
    abstract fun reportTableDao(): ReportTableDao
    
    companion object {
        @Volatile
        private var INSTANCE: ReportDatabase? = null
        
        private const val DATABASE_NAME = "project_reports.db"
        
        fun getDatabase(context: Context): ReportDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    ReportDatabase::class.java,
                    DATABASE_NAME
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}
