package com.reportgenerator.app.network.model

import com.google.gson.annotations.SerializedName

/**
 * OpenAI API 请求模型
 */
data class ChatCompletionRequest(
    val model: String,
    val messages: List<ChatMessage>,
    val temperature: Double = 0.2,
    @SerializedName("max_tokens")
    val maxTokens: Int? = null
)

/**
 * 聊天消息
 */
data class ChatMessage(
    val role: String,
    val content: String
) {
    companion object {
        fun system(content: String) = ChatMessage("system", content)
        fun user(content: String) = ChatMessage("user", content)
        fun assistant(content: String) = ChatMessage("assistant", content)
    }
}

/**
 * OpenAI API 响应模型
 */
data class ChatCompletionResponse(
    val id: String,
    val `object`: String,
    val created: Long,
    val model: String,
    val choices: List<ChatChoice>,
    val usage: Usage?
)

/**
 * 选择项
 */
data class ChatChoice(
    val index: Int,
    val message: ChatMessage,
    @SerializedName("finish_reason")
    val finishReason: String?
)

/**
 * 使用情况
 */
data class Usage(
    @SerializedName("prompt_tokens")
    val promptTokens: Int,
    @SerializedName("completion_tokens")
    val completionTokens: Int,
    @SerializedName("total_tokens")
    val totalTokens: Int
)

/**
 * API 错误响应
 */
data class ApiErrorResponse(
    val error: ApiError
)

/**
 * API 错误详情
 */
data class ApiError(
    val message: String,
    val type: String,
    val param: String?,
    val code: String?
)

/**
 * 解析结果
 */
data class ParseResult(
    val success: Boolean,
    val data: List<Map<String, String>>? = null,
    val error: String? = null
)
