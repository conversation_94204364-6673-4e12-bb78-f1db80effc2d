package com.reportgenerator.app.ui

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.google.android.material.snackbar.Snackbar
import com.reportgenerator.app.R
import com.reportgenerator.app.databinding.ActivityMainBinding
import com.reportgenerator.app.ui.viewmodel.MainViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * 主Activity
 */
@AndroidEntryPoint
class MainActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityMainBinding
    private val viewModel: MainViewModel by viewModels()
    
    // 权限请求启动器
    private val storagePermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            showMessage("存储权限已授予")
        } else {
            showMessage("需要存储权限才能导出图片")
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        observeViewModel()
        checkPermissions()
    }
    
    private fun setupUI() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.title = getString(R.string.app_name)
        
        // 设置输入区域
        setupInputSection()
        
        // 设置操作按钮
        setupActionButtons()
    }
    
    private fun setupInputSection() {
        binding.inputText.hint = getString(R.string.input_text_hint)
        
        binding.generateButton.setOnClickListener {
            val text = binding.inputText.text.toString().trim()
            if (text.isEmpty()) {
                showMessage(getString(R.string.empty_text_warning))
                binding.inputText.requestFocus()
                return@setOnClickListener
            }
            
            viewModel.generateTableFromText(text)
        }
        
        binding.clearButton.setOnClickListener {
            binding.inputText.text?.clear()
        }
        
        binding.pasteButton.setOnClickListener {
            // 这里可以实现从剪贴板粘贴的功能
            showMessage("请手动粘贴文本")
        }
    }
    
    private fun setupActionButtons() {
        binding.addRowButton.setOnClickListener {
            viewModel.addRow()
        }
        
        binding.deleteRowButton.setOnClickListener {
            viewModel.deleteLastRow()
        }
        
        binding.addColumnButton.setOnClickListener {
            showAddColumnDialog()
        }
        
        binding.deleteColumnButton.setOnClickListener {
            showDeleteColumnDialog()
        }
        
        binding.saveReportButton.setOnClickListener {
            showSaveReportDialog()
        }
        
        binding.exportImageButton.setOnClickListener {
            if (hasStoragePermission()) {
                viewModel.exportTableAsImage()
            } else {
                requestStoragePermission()
            }
        }
    }
    
    private fun observeViewModel() {
        // 观察加载状态
        viewModel.isLoading.observe(this) { isLoading ->
            binding.generateButton.isEnabled = !isLoading
            binding.generateButton.text = if (isLoading) {
                getString(R.string.generating)
            } else {
                getString(R.string.generate_table)
            }
            
            if (isLoading) {
                binding.progressBar.show()
            } else {
                binding.progressBar.hide()
            }
        }
        
        // 观察错误信息
        viewModel.errorMessage.observe(this) { error ->
            error?.let {
                showError(it)
                viewModel.clearError()
            }
        }
        
        // 观察成功信息
        viewModel.successMessage.observe(this) { message ->
            message?.let {
                showMessage(it)
                viewModel.clearSuccessMessage()
            }
        }
        
        // 观察当前报表
        viewModel.currentReport.observe(this) { report ->
            updateTableInfo(report)
            updateActionButtonsState(report != null)
        }
        
        // 观察历史报表
        viewModel.historyReports.observe(this) { reports ->
            updateHistoryList(reports)
        }
    }
    
    private fun updateTableInfo(report: com.reportgenerator.app.data.model.ReportTable?) {
        if (report != null) {
            binding.tableInfoText.text = getString(
                R.string.table_info,
                report.rowCount,
                report.columnCount
            )
            binding.tableInfoText.visibility = android.view.View.VISIBLE
            binding.noTableText.visibility = android.view.View.GONE
        } else {
            binding.tableInfoText.visibility = android.view.View.GONE
            binding.noTableText.visibility = android.view.View.VISIBLE
        }
    }
    
    private fun updateActionButtonsState(hasTable: Boolean) {
        binding.addRowButton.isEnabled = hasTable
        binding.deleteRowButton.isEnabled = hasTable
        binding.addColumnButton.isEnabled = hasTable
        binding.deleteColumnButton.isEnabled = hasTable
        binding.saveReportButton.isEnabled = hasTable
        binding.exportImageButton.isEnabled = hasTable
    }
    
    private fun updateHistoryList(reports: List<com.reportgenerator.app.data.model.ReportTable>) {
        // 这里会在后续实现RecyclerView适配器时完善
        binding.historyCountText.text = "历史报表: ${reports.size}"
    }
    
    private fun showAddColumnDialog() {
        val dialog = androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(R.string.dialog_add_column_title)
            .setView(R.layout.dialog_input)
            .setPositiveButton(R.string.ok) { dialog, _ ->
                val input = (dialog as androidx.appcompat.app.AlertDialog)
                    .findViewById<android.widget.EditText>(R.id.input_text)
                val columnName = input?.text?.toString()?.trim()
                if (!columnName.isNullOrEmpty()) {
                    viewModel.addColumn(columnName)
                }
            }
            .setNegativeButton(R.string.cancel, null)
            .create()
        
        dialog.show()
        dialog.findViewById<android.widget.EditText>(R.id.input_text)?.hint = 
            getString(R.string.dialog_add_column_hint)
    }
    
    private fun showDeleteColumnDialog() {
        // 这里会在实现表格编辑功能时完善
        showMessage("删除列功能开发中")
    }
    
    private fun showSaveReportDialog() {
        val dialog = androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(R.string.dialog_save_title)
            .setView(R.layout.dialog_input)
            .setPositiveButton(R.string.save) { dialog, _ ->
                val input = (dialog as androidx.appcompat.app.AlertDialog)
                    .findViewById<android.widget.EditText>(R.id.input_text)
                val title = input?.text?.toString()?.trim()
                if (!title.isNullOrEmpty()) {
                    viewModel.saveCurrentReport(title)
                }
            }
            .setNegativeButton(R.string.cancel, null)
            .create()
        
        dialog.show()
        dialog.findViewById<android.widget.EditText>(R.id.input_text)?.hint = 
            getString(R.string.dialog_save_hint)
    }
    
    private fun checkPermissions() {
        if (!hasStoragePermission()) {
            // 可以在这里显示权限说明
        }
    }
    
    private fun hasStoragePermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            this,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    private fun requestStoragePermission() {
        storagePermissionLauncher.launch(
            arrayOf(
                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                Manifest.permission.READ_EXTERNAL_STORAGE
            )
        )
    }
    
    private fun showMessage(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    private fun showError(error: String) {
        Snackbar.make(binding.root, error, Snackbar.LENGTH_LONG).show()
    }
}
