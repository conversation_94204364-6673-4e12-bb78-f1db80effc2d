import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/report_table.dart';

/// 图片导出服务
class ImageExportService {
  static const double _cellHeight = 60.0;
  static const double _minCellWidth = 120.0;
  static const double _maxCellWidth = 400.0;
  static const double _padding = 20.0;
  static const double _fontSize = 16.0;

  /// 将表格导出为图片
  Future<void> exportTableAsImage(ReportTable report) async {
    // 请求存储权限
    final permission = await _requestStoragePermission();
    if (!permission) {
      throw Exception('需要存储权限才能保存图片');
    }

    // 创建表格图片
    final imageBytes = await _createTableImage(report);
    
    // 保存图片到设备
    await _saveImageToDevice(imageBytes, report.title);
  }

  /// 请求存储权限
  Future<bool> _requestStoragePermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      return status.isGranted;
    }
    return true; // iOS不需要特殊权限
  }

  /// 创建表格图片
  Future<Uint8List> _createTableImage(ReportTable report) async {
    // 计算表格尺寸
    final dimensions = _calculateTableDimensions(report);
    
    // 创建画布
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    
    // 绘制表格
    await _drawTable(canvas, report, dimensions);
    
    // 转换为图片
    final picture = recorder.endRecording();
    final image = await picture.toImage(
      dimensions.totalWidth.toInt(),
      dimensions.totalHeight.toInt(),
    );
    
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    return byteData!.buffer.asUint8List();
  }

  /// 计算表格尺寸
  TableDimensions _calculateTableDimensions(ReportTable report) {
    final columnWidths = <double>[];
    
    // 计算每列宽度
    for (int col = 0; col < report.headers.length; col++) {
      double maxWidth = _calculateTextWidth(report.headers[col], _fontSize, true);
      
      // 检查数据行的宽度
      for (int row = 0; row < report.data.length; row++) {
        if (col < report.data[row].length) {
          final cellWidth = _calculateTextWidth(report.data[row][col], _fontSize, false);
          if (cellWidth > maxWidth) {
            maxWidth = cellWidth;
          }
        }
      }
      
      // 限制宽度范围并添加内边距
      maxWidth = (maxWidth + _padding * 2).clamp(_minCellWidth, _maxCellWidth);
      columnWidths.add(maxWidth);
    }
    
    final totalWidth = columnWidths.reduce((a, b) => a + b) + _padding * 2;
    final totalHeight = (report.data.length + 1) * _cellHeight + _padding * 2;
    
    return TableDimensions(
      columnWidths: columnWidths,
      totalWidth: totalWidth,
      totalHeight: totalHeight,
    );
  }

  /// 计算文本宽度
  double _calculateTextWidth(String text, double fontSize, bool isBold) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: TextStyle(
          fontSize: fontSize,
          fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    return textPainter.width;
  }

  /// 绘制表格
  Future<void> _drawTable(Canvas canvas, ReportTable report, TableDimensions dimensions) async {
    // 绘制背景
    final backgroundPaint = Paint()..color = Colors.white;
    canvas.drawRect(
      Rect.fromLTWH(0, 0, dimensions.totalWidth, dimensions.totalHeight),
      backgroundPaint,
    );

    // 绘制表头
    await _drawTableHeader(canvas, report, dimensions);
    
    // 绘制数据行
    await _drawTableData(canvas, report, dimensions);
    
    // 绘制边框
    _drawTableBorders(canvas, report, dimensions);
  }

  /// 绘制表头
  Future<void> _drawTableHeader(Canvas canvas, ReportTable report, TableDimensions dimensions) async {
    final headerPaint = Paint()..color = Colors.grey.shade200;
    
    double x = _padding;
    final y = _padding;
    
    for (int col = 0; col < report.headers.length; col++) {
      final cellWidth = dimensions.columnWidths[col];
      
      // 绘制表头背景
      canvas.drawRect(
        Rect.fromLTWH(x, y, cellWidth, _cellHeight),
        headerPaint,
      );
      
      // 绘制表头文本
      await _drawText(
        canvas,
        report.headers[col],
        x + _padding / 2,
        y + _cellHeight / 2,
        cellWidth - _padding,
        _fontSize,
        true,
        Colors.black,
        TextAlign.center,
      );
      
      x += cellWidth;
    }
  }

  /// 绘制表格数据
  Future<void> _drawTableData(Canvas canvas, ReportTable report, TableDimensions dimensions) async {
    double y = _padding + _cellHeight;
    
    for (int row = 0; row < report.data.length; row++) {
      double x = _padding;
      
      for (int col = 0; col < report.headers.length; col++) {
        final cellWidth = dimensions.columnWidths[col];
        final cellValue = col < report.data[row].length ? report.data[row][col] : '';
        
        // 绘制单元格文本
        await _drawText(
          canvas,
          cellValue,
          x + _padding / 2,
          y + _cellHeight / 2,
          cellWidth - _padding,
          _fontSize,
          false,
          Colors.black,
          TextAlign.left,
        );
        
        x += cellWidth;
      }
      
      y += _cellHeight;
    }
  }

  /// 绘制表格边框
  void _drawTableBorders(Canvas canvas, ReportTable report, TableDimensions dimensions) {
    final borderPaint = Paint()
      ..color = Colors.grey.shade400
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // 绘制外边框
    canvas.drawRect(
      Rect.fromLTWH(_padding, _padding, 
        dimensions.totalWidth - _padding * 2, 
        dimensions.totalHeight - _padding * 2),
      borderPaint,
    );

    // 绘制垂直线
    double x = _padding;
    for (int col = 0; col < report.headers.length - 1; col++) {
      x += dimensions.columnWidths[col];
      canvas.drawLine(
        Offset(x, _padding),
        Offset(x, dimensions.totalHeight - _padding),
        borderPaint,
      );
    }

    // 绘制水平线
    double y = _padding + _cellHeight;
    for (int row = 0; row < report.data.length; row++) {
      canvas.drawLine(
        Offset(_padding, y),
        Offset(dimensions.totalWidth - _padding, y),
        borderPaint,
      );
      y += _cellHeight;
    }
  }

  /// 绘制文本
  Future<void> _drawText(
    Canvas canvas,
    String text,
    double x,
    double y,
    double maxWidth,
    double fontSize,
    bool isBold,
    Color color,
    TextAlign textAlign,
  ) async {
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: TextStyle(
          fontSize: fontSize,
          fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
          color: color,
        ),
      ),
      textDirection: TextDirection.ltr,
      textAlign: textAlign,
      maxLines: 2,
      ellipsis: '...',
    );
    
    textPainter.layout(maxWidth: maxWidth);
    
    // 计算垂直居中位置
    final textY = y - textPainter.height / 2;
    
    textPainter.paint(canvas, Offset(x, textY));
  }

  /// 保存图片到设备
  Future<void> _saveImageToDevice(Uint8List imageBytes, String fileName) async {
    final directory = await getExternalStorageDirectory();
    if (directory == null) {
      throw Exception('无法访问存储目录');
    }
    
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final file = File('${directory.path}/${fileName}_$timestamp.png');
    
    await file.writeAsBytes(imageBytes);
  }
}

/// 表格尺寸信息
class TableDimensions {
  final List<double> columnWidths;
  final double totalWidth;
  final double totalHeight;

  TableDimensions({
    required this.columnWidths,
    required this.totalWidth,
    required this.totalHeight,
  });
}
