package com.reportgenerator.app.network.service

import android.util.Log
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import com.reportgenerator.app.data.model.ReportTable
import com.reportgenerator.app.network.api.OpenAIApi
import com.reportgenerator.app.network.model.ChatCompletionRequest
import com.reportgenerator.app.network.model.ChatMessage
import com.reportgenerator.app.network.model.ParseResult
import com.reportgenerator.app.utils.Constants
import kotlinx.coroutines.delay
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

/**
 * AI 服务类
 */
@Singleton
class AIService @Inject constructor(
    private val openAIApi: OpenAIApi,
    private val gson: Gson
) {
    
    companion object {
        private const val TAG = "AIService"
        private const val MAX_RETRIES = 3
        private const val RETRY_DELAY_MS = 1000L
    }
    
    /**
     * 将项目汇报文本转换为表格数据
     */
    suspend fun parseTextToTable(reportText: String): Result<ReportTable> {
        if (reportText.trim().isEmpty()) {
            return Result.failure(IllegalArgumentException("输入文本不能为空"))
        }
        
        return try {
            val parseResult = callAIWithRetry(reportText)
            if (parseResult.success && parseResult.data != null) {
                val reportTable = createReportTable(parseResult.data)
                Result.success(reportTable)
            } else {
                Result.failure(Exception(parseResult.error ?: "AI解析失败"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "解析文本失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 调用AI API，包含重试逻辑
     */
    private suspend fun callAIWithRetry(text: String): ParseResult {
        val prompt = buildPrompt(text)
        
        repeat(MAX_RETRIES) { attempt ->
            try {
                val request = ChatCompletionRequest(
                    model = Constants.AI_MODEL,
                    messages = listOf(
                        ChatMessage.system("你是一个擅长结构化信息提取的AI助手。请严格按照JSON格式返回数据。"),
                        ChatMessage.user(prompt)
                    ),
                    temperature = 0.2
                )
                
                val response = openAIApi.createChatCompletion(request)
                
                if (response.isSuccessful) {
                    val chatResponse = response.body()
                    if (chatResponse != null && chatResponse.choices.isNotEmpty()) {
                        val content = chatResponse.choices[0].message.content
                        val extractedData = extractJsonFromResponse(content)
                        if (extractedData != null) {
                            return ParseResult(success = true, data = extractedData)
                        }
                    }
                } else {
                    Log.w(TAG, "API错误 (尝试 ${attempt + 1}/$MAX_RETRIES): ${response.code()} - ${response.message()}")
                }
            } catch (e: Exception) {
                Log.w(TAG, "API调用异常 (尝试 ${attempt + 1}/$MAX_RETRIES)", e)
            }
            
            if (attempt < MAX_RETRIES - 1) {
                delay(RETRY_DELAY_MS * (attempt + 1)) // 递增延迟
            }
        }
        
        return ParseResult(success = false, error = "AI服务调用失败，请稍后重试")
    }
    
    /**
     * 构建AI提示词
     */
    private fun buildPrompt(text: String): String {
        val headers = ReportTable.DEFAULT_HEADERS.joinToString("、")
        return """
请将以下项目汇报文本智能解析为标准表格，每个项目一行，字段包括：$headers。

要求：
1. 严格按照JSON数组格式输出，每行一个项目对象
2. 如果某个字段信息不明确，请填写"待确认"
3. 融资额请提取数字部分，单位统一为亿元
4. 最近更新日期如果文本中没有，请填写今天的日期

输入文本：
$text

请返回JSON格式：
[
  {
    "项目名称": "项目名称",
    "融资额 (亿元)": "数字",
    "项目状态": "状态",
    "核心进展": "进展描述",
    "下一步工作计划": "计划描述",
    "难点与风险": "风险描述",
    "最近更新日期": "日期"
  }
]
        """.trimIndent()
    }
    
    /**
     * 从AI响应中提取JSON数据
     */
    private fun extractJsonFromResponse(content: String): List<Map<String, String>>? {
        return try {
            // 尝试直接解析
            val listType = object : TypeToken<List<Map<String, String>>>() {}.type
            gson.fromJson<List<Map<String, String>>>(content, listType)
        } catch (e: JsonSyntaxException) {
            // 如果直接解析失败，尝试提取JSON部分
            val jsonRegex = Regex("""\[.*\]""", RegexOption.DOT_MATCHES_ALL)
            val jsonMatch = jsonRegex.find(content)
            if (jsonMatch != null) {
                try {
                    val listType = object : TypeToken<List<Map<String, String>>>() {}.type
                    gson.fromJson<List<Map<String, String>>>(jsonMatch.value, listType)
                } catch (e: JsonSyntaxException) {
                    Log.e(TAG, "JSON提取失败", e)
                    null
                }
            } else {
                Log.e(TAG, "未找到JSON格式数据")
                null
            }
        }
    }
    
    /**
     * 创建报表对象
     */
    private fun createReportTable(data: List<Map<String, String>>): ReportTable {
        if (data.isEmpty()) {
            throw IllegalArgumentException("AI返回的数据为空")
        }
        
        // 使用第一行数据的键作为表头
        val headers = data.first().keys.toList()
        
        // 确保表头顺序与默认表头一致
        val orderedHeaders = mutableListOf<String>()
        for (defaultHeader in ReportTable.DEFAULT_HEADERS) {
            if (headers.contains(defaultHeader)) {
                orderedHeaders.add(defaultHeader)
            }
        }
        // 添加其他未匹配的表头
        for (header in headers) {
            if (!orderedHeaders.contains(header)) {
                orderedHeaders.add(header)
            }
        }
        
        // 转换数据为二维数组
        val tableData = data.map { row ->
            orderedHeaders.map { header -> row[header] ?: "" }
        }
        
        return ReportTable(
            title = "报表_${java.text.SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", java.util.Locale.getDefault()).format(Date())}",
            headers = orderedHeaders,
            data = tableData,
            createdAt = Date()
        )
    }
}
