package com.reportgenerator.app.ui.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.reportgenerator.app.data.model.ReportTable
import com.reportgenerator.app.data.repository.ReportRepository
import com.reportgenerator.app.network.service.AIService
import com.reportgenerator.app.service.ImageExportService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 主界面ViewModel
 */
@HiltViewModel
class MainViewModel @Inject constructor(
    private val reportRepository: ReportRepository,
    private val aiService: AIService,
    private val imageExportService: ImageExportService
) : ViewModel() {
    
    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    // 错误信息
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    // 成功信息
    private val _successMessage = MutableLiveData<String?>()
    val successMessage: LiveData<String?> = _successMessage
    
    // 当前报表
    private val _currentReport = MutableLiveData<ReportTable?>()
    val currentReport: LiveData<ReportTable?> = _currentReport
    
    // 历史报表
    val historyReports: LiveData<List<ReportTable>> = reportRepository.getAllReports()
    
    /**
     * 从文本生成表格
     */
    fun generateTableFromText(text: String) {
        if (text.trim().isEmpty()) {
            _errorMessage.value = "请输入项目汇报文本"
            return
        }
        
        viewModelScope.launch {
            _isLoading.value = true
            _errorMessage.value = null
            
            try {
                val result = aiService.parseTextToTable(text)
                result.fold(
                    onSuccess = { reportTable ->
                        _currentReport.value = reportTable
                        _successMessage.value = "表格生成成功！"
                    },
                    onFailure = { exception ->
                        _errorMessage.value = "生成失败: ${exception.message}"
                    }
                )
            } catch (e: Exception) {
                _errorMessage.value = "生成失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 保存当前报表
     */
    fun saveCurrentReport(title: String) {
        val report = _currentReport.value
        if (report == null) {
            _errorMessage.value = "没有可保存的报表"
            return
        }
        
        viewModelScope.launch {
            try {
                val reportToSave = report.copy(title = title)
                reportRepository.saveReport(reportToSave)
                _successMessage.value = "报表保存成功！"
            } catch (e: Exception) {
                _errorMessage.value = "保存失败: ${e.message}"
            }
        }
    }
    
    /**
     * 加载历史报表
     */
    fun loadHistoryReport(reportId: Long) {
        viewModelScope.launch {
            try {
                val report = reportRepository.getReportById(reportId)
                if (report != null) {
                    _currentReport.value = report
                } else {
                    _errorMessage.value = "报表不存在"
                }
            } catch (e: Exception) {
                _errorMessage.value = "加载失败: ${e.message}"
            }
        }
    }
    
    /**
     * 删除历史报表
     */
    fun deleteHistoryReport(reportId: Long) {
        viewModelScope.launch {
            try {
                reportRepository.deleteReportById(reportId)
                _successMessage.value = "删除成功！"
                
                // 如果删除的是当前报表，清除当前报表
                val currentReport = _currentReport.value
                if (currentReport?.id == reportId) {
                    _currentReport.value = null
                }
            } catch (e: Exception) {
                _errorMessage.value = "删除失败: ${e.message}"
            }
        }
    }
    
    /**
     * 添加新行
     */
    fun addRow() {
        val report = _currentReport.value
        if (report != null) {
            _currentReport.value = report.addRow()
        }
    }
    
    /**
     * 删除最后一行
     */
    fun deleteLastRow() {
        val report = _currentReport.value
        if (report != null && report.rowCount > 0) {
            _currentReport.value = report.deleteRow(report.rowCount - 1)
        }
    }
    
    /**
     * 添加新列
     */
    fun addColumn(headerName: String) {
        val report = _currentReport.value
        if (report != null) {
            _currentReport.value = report.addColumn(headerName)
        }
    }
    
    /**
     * 删除列
     */
    fun deleteColumn(columnIndex: Int) {
        val report = _currentReport.value
        if (report != null) {
            _currentReport.value = report.deleteColumn(columnIndex)
        }
    }
    
    /**
     * 更新单元格数据
     */
    fun updateCellData(row: Int, column: Int, value: String) {
        val report = _currentReport.value
        if (report != null) {
            _currentReport.value = report.updateCellData(row, column, value)
        }
    }
    
    /**
     * 更新表头
     */
    fun updateHeader(columnIndex: Int, newName: String) {
        val report = _currentReport.value
        if (report != null) {
            _currentReport.value = report.updateHeader(columnIndex, newName)
        }
    }
    
    /**
     * 导出表格为图片
     */
    fun exportTableAsImage() {
        val report = _currentReport.value
        if (report == null) {
            _errorMessage.value = "没有可导出的表格"
            return
        }
        
        viewModelScope.launch {
            try {
                imageExportService.exportTableAsImage(report)
                _successMessage.value = "图片导出成功！"
            } catch (e: Exception) {
                _errorMessage.value = "导出失败: ${e.message}"
            }
        }
    }
    
    /**
     * 创建新的空报表
     */
    fun createNewReport() {
        _currentReport.value = ReportTable.createEmpty()
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _errorMessage.value = null
    }
    
    /**
     * 清除成功信息
     */
    fun clearSuccessMessage() {
        _successMessage.value = null
    }
}
