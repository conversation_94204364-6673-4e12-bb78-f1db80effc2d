# 一键报表生成器 Flutter版

这是一个基于Flutter开发的安卓应用，功能与原Python版本的`main.py`完全相同，能够智能解析项目汇报文本并自动生成结构化表格。

## 功能特性

### 🤖 AI智能解析
- 使用OpenAI API智能解析项目汇报文本
- 自动提取项目名称、融资额、进展状态等关键信息
- 支持多种文本格式和表达方式

### 📊 表格编辑
- 双击单元格进行编辑
- 支持添加/删除行和列
- 可编辑表头名称
- 动态调整列宽

### 💾 数据持久化
- 使用SQLite数据库保存历史报表
- 支持报表的加载、保存和删除
- 自动记录创建时间

### 🖼️ 图片导出
- 将表格导出为高质量PNG图片
- 自动调整表格布局和字体大小
- 保存到设备存储

### 📱 移动端优化
- 响应式布局，适配不同屏幕尺寸
- 流畅的滚动和缩放体验
- Material Design风格界面

## 技术栈

- **框架**: Flutter 3.0+
- **状态管理**: Provider
- **数据库**: SQLite (sqflite)
- **网络请求**: Dio
- **图片处理**: Flutter Canvas API
- **权限管理**: permission_handler

## 项目结构

```
lib/
├── main.dart                 # 应用入口
├── models/                   # 数据模型
│   └── report_table.dart
├── services/                 # 服务层
│   ├── database_service.dart # 数据库服务
│   ├── ai_service.dart       # AI服务
│   └── image_export_service.dart # 图片导出服务
├── providers/                # 状态管理
│   └── report_provider.dart
├── screens/                  # 页面
│   └── home_screen.dart
├── widgets/                  # UI组件
│   ├── history_panel.dart    # 历史面板
│   ├── input_panel.dart      # 输入面板
│   ├── table_panel.dart      # 表格面板
│   └── action_panel.dart     # 操作面板
└── utils/                    # 工具类
    ├── constants.dart        # 常量配置
    └── logger.dart          # 日志工具
```

## 安装和运行

### 前置要求

1. **Flutter SDK**: 3.0.0 或更高版本
2. **Android Studio** 或 **VS Code** (推荐安装Flutter插件)
3. **Android设备** 或 **模拟器** (API 21+)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd flutter_app
   ```

2. **安装依赖**
   ```bash
   flutter pub get
   ```

3. **配置API密钥**
   
   在 `lib/utils/constants.dart` 中修改API配置：
   ```dart
   static const String apiKey = "your-openai-api-key";
   static const String apiBaseUrl = "your-api-base-url";
   ```

4. **运行应用**
   ```bash
   flutter run
   ```

### 构建APK

```bash
# 构建调试版APK
flutter build apk --debug

# 构建发布版APK
flutter build apk --release
```

## 使用说明

### 1. 文本输入
- 在输入框中粘贴或输入项目汇报文本
- 支持多种格式，如带有表情符号的进展标记
- 点击"一键生成表格"按钮进行AI解析

### 2. 表格编辑
- **编辑单元格**: 双击任意单元格进行编辑
- **编辑表头**: 点击表头进行重命名
- **添加行**: 点击"添加行"按钮
- **删除行**: 点击"删除行"按钮删除最后一行
- **添加列**: 点击"添加列"按钮并输入列名
- **删除列**: 点击"删除列"按钮并选择要删除的列

### 3. 数据管理
- **保存报表**: 点击"保存报表"按钮并输入标题
- **加载历史**: 在左侧历史面板中点击任意报表
- **删除历史**: 点击历史报表右侧的菜单按钮选择删除

### 4. 导出功能
- **导出图片**: 点击"导出图片"按钮将表格保存为PNG图片
- 图片会保存到设备的外部存储目录

## 权限说明

应用需要以下权限：

- **网络访问权限**: 用于调用AI API
- **存储权限**: 用于保存导出的图片文件

## 示例文本格式

```
1. 汈汊湖项目（融资额5亿）
✅ 核心进展： 完成住建、自规、水泊局、发改等多部门批复。
➡ 下一步工作：继续协助其他手续办理。

2. 绿美乡村项目（融资额2.6亿）
✅核心进展：调整版融资方案已经与银行、国投沟通，待确定。
➡ 下一步工作：现场继续踏勘，确保苗木绿化种植需求。
‼ 难点：林地需要办理林权证。
```

## 故障排除

### 常见问题

1. **网络连接失败**
   - 检查网络连接
   - 确认API密钥和基础URL正确
   - 检查防火墙设置

2. **权限被拒绝**
   - 在设备设置中手动授予存储权限
   - 重启应用

3. **AI解析失败**
   - 检查输入文本格式
   - 确认API配额充足
   - 查看错误日志

### 调试模式

在调试模式下，应用会输出详细的日志信息，可以通过以下命令查看：

```bash
flutter logs
```

## 版本历史

- **v1.0.0**: 初始版本，包含所有核心功能

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目采用MIT许可证。
