package com.reportgenerator.app.utils

/**
 * 应用常量
 */
object Constants {
    
    // API 配置
    const val API_KEY = "sk-c5I0FtKg8D8VScXOEWJBDja4e8ibiJkxsLI3IvGM9B6GrLmZ"
    const val API_BASE_URL = "https://api.chatanywhere.tech/v1/"
    const val AI_MODEL = "gpt-4.1"
    
    // 网络配置
    const val CONNECT_TIMEOUT = 30L // 秒
    const val READ_TIMEOUT = 30L // 秒
    const val WRITE_TIMEOUT = 30L // 秒
    
    // 数据库配置
    const val DATABASE_NAME = "project_reports.db"
    const val DATABASE_VERSION = 1
    
    // UI 配置
    const val ANIMATION_DURATION = 300L // 毫秒
    const val DEBOUNCE_DELAY = 500L // 毫秒
    
    // 文件配置
    const val EXPORT_IMAGE_QUALITY = 100
    const val EXPORT_IMAGE_FORMAT = "PNG"
    const val EXPORT_DIRECTORY_NAME = "ReportGenerator"
    
    // 权限请求码
    const val REQUEST_STORAGE_PERMISSION = 1001
    
    // SharedPreferences 键
    const val PREF_NAME = "report_generator_prefs"
    const val PREF_FIRST_LAUNCH = "first_launch"
    const val PREF_LAST_BACKUP_TIME = "last_backup_time"
    
    // 示例文本
    const val SAMPLE_TEXT = """1. 汈汊湖项目（融资额5亿）
✅ 核心进展： 完成住建、自规、水泊局、发改等多部门批复。
➡ 下一步工作：继续协助其他手续办理。

2. 绿美乡村项目（融资额2.6亿）
✅核心进展：调整版融资方案已经与银行、国投沟通，待确定。
➡ 下一步工作：现场继续踏勘，确保苗木绿化种植需求。 与国投确认林权证办理事宜。
‼ 难点：林地需要办理林权证。

3. 城区供水提质增效项目（融资额9.6亿）
4. 乡镇供水巩固提项目（融资额9亿）
✅核心进展：项目方案确定中。
➡ 下一步工作：编制项目建议书。

5. 全域交通配套项目（融资额4亿）
✅核心进展：可研编制中，目前因投资受限，或需调整贷款额。
    
6. 集贸片区改造项目（融资额4亿）
✅核心进展：方案编制中。"""
}
