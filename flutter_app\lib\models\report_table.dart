import 'dart:convert';

/// 报表数据模型
class ReportTable {
  final int? id;
  final String title;
  final List<String> headers;
  final List<List<String>> data;
  final DateTime createdAt;

  ReportTable({
    this.id,
    required this.title,
    required this.headers,
    required this.data,
    required this.createdAt,
  });

  /// 从数据库记录创建对象
  factory ReportTable.fromMap(Map<String, dynamic> map) {
    return ReportTable(
      id: map['id'],
      title: map['title'],
      headers: List<String>.from(json.decode(map['headers'])),
      data: List<List<String>>.from(
        json.decode(map['data']).map((row) => List<String>.from(row))
      ),
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  /// 转换为数据库记录
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'headers': json.encode(headers),
      'data': json.encode(data),
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// 复制对象并修改部分属性
  ReportTable copyWith({
    int? id,
    String? title,
    List<String>? headers,
    List<List<String>>? data,
    DateTime? createdAt,
  }) {
    return ReportTable(
      id: id ?? this.id,
      title: title ?? this.title,
      headers: headers ?? this.headers,
      data: data ?? this.data,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'ReportTable{id: $id, title: $title, headers: $headers, data: $data, createdAt: $createdAt}';
  }
}

/// 表格单元格数据
class TableCell {
  final int row;
  final int column;
  final String value;

  TableCell({
    required this.row,
    required this.column,
    required this.value,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TableCell &&
          runtimeType == other.runtimeType &&
          row == other.row &&
          column == other.column;

  @override
  int get hashCode => row.hashCode ^ column.hashCode;
}
