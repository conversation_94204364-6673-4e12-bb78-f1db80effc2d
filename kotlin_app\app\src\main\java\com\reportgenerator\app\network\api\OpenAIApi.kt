package com.reportgenerator.app.network.api

import com.reportgenerator.app.network.model.ChatCompletionRequest
import com.reportgenerator.app.network.model.ChatCompletionResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * OpenAI API 接口
 */
interface OpenAIApi {
    
    @POST("chat/completions")
    suspend fun createChatCompletion(
        @Body request: ChatCompletionRequest
    ): Response<ChatCompletionResponse>
}
