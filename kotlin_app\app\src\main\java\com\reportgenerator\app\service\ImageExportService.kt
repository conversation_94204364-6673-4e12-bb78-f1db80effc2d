package com.reportgenerator.app.service

import android.content.Context
import android.graphics.*
import android.os.Environment
import com.reportgenerator.app.data.model.ReportTable
import com.reportgenerator.app.utils.Constants
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.max

/**
 * 图片导出服务
 */
@Singleton
class ImageExportService @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val CELL_HEIGHT = 80f
        private const val MIN_CELL_WIDTH = 150f
        private const val MAX_CELL_WIDTH = 500f
        private const val PADDING = 30f
        private const val TEXT_SIZE = 20f
        private const val HEADER_TEXT_SIZE = 22f
    }
    
    /**
     * 将表格导出为图片
     */
    suspend fun exportTableAsImage(reportTable: ReportTable) = withContext(Dispatchers.IO) {
        val bitmap = createTableBitmap(reportTable)
        saveImageToStorage(bitmap, reportTable.title)
    }
    
    /**
     * 创建表格位图
     */
    private fun createTableBitmap(reportTable: ReportTable): Bitmap {
        val dimensions = calculateTableDimensions(reportTable)
        
        val bitmap = Bitmap.createBitmap(
            dimensions.totalWidth.toInt(),
            dimensions.totalHeight.toInt(),
            Bitmap.Config.ARGB_8888
        )
        
        val canvas = Canvas(bitmap)
        drawTable(canvas, reportTable, dimensions)
        
        return bitmap
    }
    
    /**
     * 计算表格尺寸
     */
    private fun calculateTableDimensions(reportTable: ReportTable): TableDimensions {
        val columnWidths = mutableListOf<Float>()
        
        // 创建临时画笔来测量文本
        val textPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            textSize = TEXT_SIZE
        }
        
        val headerPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            textSize = HEADER_TEXT_SIZE
            isFakeBoldText = true
        }
        
        // 计算每列宽度
        for (columnIndex in reportTable.headers.indices) {
            // 表头宽度
            var maxWidth = headerPaint.measureText(reportTable.headers[columnIndex])
            
            // 检查数据行的宽度
            for (rowIndex in reportTable.data.indices) {
                val cellValue = reportTable.getCellData(rowIndex, columnIndex)
                val cellWidth = textPaint.measureText(cellValue)
                maxWidth = max(maxWidth, cellWidth)
            }
            
            // 限制宽度范围并添加内边距
            val finalWidth = (maxWidth + PADDING * 2).coerceIn(MIN_CELL_WIDTH, MAX_CELL_WIDTH)
            columnWidths.add(finalWidth)
        }
        
        val totalWidth = columnWidths.sum() + PADDING * 2
        val totalHeight = (reportTable.data.size + 1) * CELL_HEIGHT + PADDING * 2
        
        return TableDimensions(columnWidths, totalWidth, totalHeight)
    }
    
    /**
     * 绘制表格
     */
    private fun drawTable(canvas: Canvas, reportTable: ReportTable, dimensions: TableDimensions) {
        // 绘制背景
        canvas.drawColor(Color.WHITE)
        
        // 创建画笔
        val headerPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.parseColor("#E3F2FD")
        }
        
        val cellPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.WHITE
        }
        
        val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.parseColor("#BDBDBD")
            strokeWidth = 2f
            style = Paint.Style.STROKE
        }
        
        val headerTextPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.BLACK
            textSize = HEADER_TEXT_SIZE
            isFakeBoldText = true
            textAlign = Paint.Align.CENTER
        }
        
        val cellTextPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.BLACK
            textSize = TEXT_SIZE
            textAlign = Paint.Align.LEFT
        }
        
        var currentX = PADDING
        var currentY = PADDING
        
        // 绘制表头
        for (columnIndex in reportTable.headers.indices) {
            val columnWidth = dimensions.columnWidths[columnIndex]
            val rect = RectF(currentX, currentY, currentX + columnWidth, currentY + CELL_HEIGHT)
            
            // 绘制表头背景
            canvas.drawRect(rect, headerPaint)
            
            // 绘制表头文本
            val headerText = reportTable.headers[columnIndex]
            val textX = rect.centerX()
            val textY = rect.centerY() + getTextHeight(headerTextPaint) / 2
            canvas.drawText(headerText, textX, textY, headerTextPaint)
            
            // 绘制边框
            canvas.drawRect(rect, borderPaint)
            
            currentX += columnWidth
        }
        
        // 绘制数据行
        currentY += CELL_HEIGHT
        for (rowIndex in reportTable.data.indices) {
            currentX = PADDING
            
            for (columnIndex in reportTable.headers.indices) {
                val columnWidth = dimensions.columnWidths[columnIndex]
                val rect = RectF(currentX, currentY, currentX + columnWidth, currentY + CELL_HEIGHT)
                
                // 绘制单元格背景
                canvas.drawRect(rect, cellPaint)
                
                // 绘制单元格文本
                val cellValue = reportTable.getCellData(rowIndex, columnIndex)
                val clippedText = clipTextToWidth(cellValue, columnWidth - PADDING * 2, cellTextPaint)
                val textX = rect.left + PADDING
                val textY = rect.centerY() + getTextHeight(cellTextPaint) / 2
                canvas.drawText(clippedText, textX, textY, cellTextPaint)
                
                // 绘制边框
                canvas.drawRect(rect, borderPaint)
                
                currentX += columnWidth
            }
            
            currentY += CELL_HEIGHT
        }
        
        // 绘制外边框
        val outerRect = RectF(PADDING, PADDING, 
            dimensions.totalWidth - PADDING, dimensions.totalHeight - PADDING)
        canvas.drawRect(outerRect, borderPaint.apply { strokeWidth = 4f })
    }
    
    /**
     * 获取文本高度
     */
    private fun getTextHeight(paint: Paint): Float {
        val fontMetrics = paint.fontMetrics
        return fontMetrics.bottom - fontMetrics.top
    }
    
    /**
     * 裁剪文本以适应指定宽度
     */
    private fun clipTextToWidth(text: String, maxWidth: Float, paint: Paint): String {
        if (paint.measureText(text) <= maxWidth) {
            return text
        }
        
        val ellipsis = "..."
        val ellipsisWidth = paint.measureText(ellipsis)
        
        var clippedText = text
        while (paint.measureText(clippedText) + ellipsisWidth > maxWidth && clippedText.isNotEmpty()) {
            clippedText = clippedText.dropLast(1)
        }
        
        return clippedText + ellipsis
    }
    
    /**
     * 保存图片到存储
     */
    private fun saveImageToStorage(bitmap: Bitmap, fileName: String) {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val filename = "${fileName}_$timestamp.png"
        
        // 获取外部存储目录
        val picturesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
        val appDir = File(picturesDir, Constants.EXPORT_DIRECTORY_NAME)
        
        if (!appDir.exists()) {
            appDir.mkdirs()
        }
        
        val file = File(appDir, filename)
        
        try {
            FileOutputStream(file).use { out ->
                bitmap.compress(Bitmap.CompressFormat.PNG, Constants.EXPORT_IMAGE_QUALITY, out)
            }
        } catch (e: Exception) {
            throw Exception("保存图片失败: ${e.message}")
        }
    }
    
    /**
     * 表格尺寸信息
     */
    private data class TableDimensions(
        val columnWidths: List<Float>,
        val totalWidth: Float,
        val totalHeight: Float
    )
}
