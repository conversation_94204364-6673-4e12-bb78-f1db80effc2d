import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/report_table.dart';

/// 数据库服务类
class DatabaseService {
  static Database? _database;
  static const String _databaseName = 'project_reports.db';
  static const String _tableName = 'saved_tables';
  static const int _databaseVersion = 1;

  /// 获取数据库实例
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// 初始化数据库
  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), _databaseName);
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
    );
  }

  /// 创建数据库表
  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE $_tableName (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        headers TEXT NOT NULL,
        data TEXT NOT NULL,
        created_at TEXT NOT NULL
      )
    ''');
  }

  /// 初始化数据库（供外部调用）
  Future<void> initDatabase() async {
    await database;
  }

  /// 保存报表
  Future<int> saveReport(ReportTable report) async {
    final db = await database;
    final map = report.toMap();
    map.remove('id'); // 移除id，让数据库自动生成
    return await db.insert(_tableName, map);
  }

  /// 获取所有报表（按创建时间倒序）
  Future<List<ReportTable>> getAllReports() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => ReportTable.fromMap(maps[i]));
  }

  /// 根据ID获取报表
  Future<ReportTable?> getReportById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return ReportTable.fromMap(maps.first);
    }
    return null;
  }

  /// 更新报表
  Future<int> updateReport(ReportTable report) async {
    final db = await database;
    return await db.update(
      _tableName,
      report.toMap(),
      where: 'id = ?',
      whereArgs: [report.id],
    );
  }

  /// 删除报表
  Future<int> deleteReport(int id) async {
    final db = await database;
    return await db.delete(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// 清空所有报表
  Future<int> clearAllReports() async {
    final db = await database;
    return await db.delete(_tableName);
  }

  /// 关闭数据库
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}
