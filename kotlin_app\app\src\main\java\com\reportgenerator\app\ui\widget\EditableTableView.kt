package com.reportgenerator.app.ui.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import androidx.core.content.ContextCompat
import com.reportgenerator.app.R
import com.reportgenerator.app.data.model.ReportTable
import kotlin.math.max
import kotlin.math.min

/**
 * 可编辑的表格视图
 */
class EditableTableView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    
    // 表格数据
    private var reportTable: ReportTable? = null
    
    // 绘制相关
    private val headerPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val cellPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val selectedPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    
    // 尺寸配置
    private val cellHeight = 60f
    private val minCellWidth = 120f
    private val maxCellWidth = 300f
    private val textPadding = 12f
    private val textSize = 14f
    
    // 表格布局
    private var columnWidths = mutableListOf<Float>()
    private var totalWidth = 0f
    private var totalHeight = 0f
    
    // 选中状态
    private var selectedRow = -1
    private var selectedColumn = -1
    
    // 滚动偏移
    private var scrollX = 0f
    private var scrollY = 0f
    
    // 手势检测
    private val gestureDetector = GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
        override fun onSingleTapUp(e: MotionEvent): Boolean {
            handleTap(e.x + scrollX, e.y + scrollY)
            return true
        }
        
        override fun onDoubleTap(e: MotionEvent): Boolean {
            handleDoubleTap(e.x + scrollX, e.y + scrollY)
            return true
        }
        
        override fun onScroll(
            e1: MotionEvent?,
            e2: MotionEvent,
            distanceX: Float,
            distanceY: Float
        ): Boolean {
            scrollX = (scrollX + distanceX).coerceIn(0f, max(0f, totalWidth - width))
            scrollY = (scrollY + distanceY).coerceIn(0f, max(0f, totalHeight - height))
            invalidate()
            return true
        }
    })
    
    // 回调接口
    interface OnTableInteractionListener {
        fun onCellClick(row: Int, column: Int, value: String)
        fun onCellDoubleClick(row: Int, column: Int, value: String)
        fun onHeaderClick(column: Int, headerName: String)
    }
    
    private var interactionListener: OnTableInteractionListener? = null
    
    init {
        setupPaints()
    }
    
    private fun setupPaints() {
        // 表头画笔
        headerPaint.color = ContextCompat.getColor(context, R.color.table_header_background)
        
        // 单元格画笔
        cellPaint.color = ContextCompat.getColor(context, android.R.color.white)
        
        // 边框画笔
        borderPaint.apply {
            color = ContextCompat.getColor(context, R.color.table_border)
            strokeWidth = 1f
            style = Paint.Style.STROKE
        }
        
        // 文本画笔
        textPaint.apply {
            color = ContextCompat.getColor(context, android.R.color.black)
            textSize = <EMAIL> * resources.displayMetrics.scaledDensity
        }
        
        // 选中状态画笔
        selectedPaint.apply {
            color = ContextCompat.getColor(context, R.color.table_selected_background)
            alpha = 128
        }
    }
    
    fun setReportTable(table: ReportTable?) {
        reportTable = table
        calculateLayout()
        invalidate()
    }
    
    fun setOnTableInteractionListener(listener: OnTableInteractionListener) {
        interactionListener = listener
    }
    
    private fun calculateLayout() {
        val table = reportTable ?: return
        
        columnWidths.clear()
        
        // 计算每列宽度
        for (columnIndex in table.headers.indices) {
            var maxWidth = getTextWidth(table.headers[columnIndex])
            
            // 检查数据行的宽度
            for (rowIndex in table.data.indices) {
                val cellValue = table.getCellData(rowIndex, columnIndex)
                val cellWidth = getTextWidth(cellValue)
                maxWidth = max(maxWidth, cellWidth)
            }
            
            // 限制宽度范围并添加内边距
            val finalWidth = (maxWidth + textPadding * 2).coerceIn(minCellWidth, maxCellWidth)
            columnWidths.add(finalWidth)
        }
        
        totalWidth = columnWidths.sum()
        totalHeight = (table.data.size + 1) * cellHeight // +1 for header
        
        requestLayout()
    }
    
    private fun getTextWidth(text: String): Float {
        return textPaint.measureText(text)
    }
    
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val desiredWidth = totalWidth.toInt()
        val desiredHeight = totalHeight.toInt()
        
        val width = resolveSize(desiredWidth, widthMeasureSpec)
        val height = resolveSize(desiredHeight, heightMeasureSpec)
        
        setMeasuredDimension(width, height)
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        val table = reportTable ?: return
        
        canvas.save()
        canvas.translate(-scrollX, -scrollY)
        
        drawTable(canvas, table)
        
        canvas.restore()
    }
    
    private fun drawTable(canvas: Canvas, table: ReportTable) {
        var currentX = 0f
        var currentY = 0f
        
        // 绘制表头
        for (columnIndex in table.headers.indices) {
            val columnWidth = columnWidths[columnIndex]
            val rect = Rect(currentX.toInt(), currentY.toInt(), 
                           (currentX + columnWidth).toInt(), (currentY + cellHeight).toInt())
            
            // 绘制表头背景
            canvas.drawRect(rect, headerPaint)
            
            // 绘制表头文本
            drawText(canvas, table.headers[columnIndex], rect, true)
            
            // 绘制边框
            canvas.drawRect(rect, borderPaint)
            
            currentX += columnWidth
        }
        
        // 绘制数据行
        currentY += cellHeight
        for (rowIndex in table.data.indices) {
            currentX = 0f
            
            for (columnIndex in table.headers.indices) {
                val columnWidth = columnWidths[columnIndex]
                val rect = Rect(currentX.toInt(), currentY.toInt(),
                               (currentX + columnWidth).toInt(), (currentY + cellHeight).toInt())
                
                // 绘制选中状态
                if (selectedRow == rowIndex && selectedColumn == columnIndex) {
                    canvas.drawRect(rect, selectedPaint)
                } else {
                    canvas.drawRect(rect, cellPaint)
                }
                
                // 绘制单元格文本
                val cellValue = table.getCellData(rowIndex, columnIndex)
                drawText(canvas, cellValue, rect, false)
                
                // 绘制边框
                canvas.drawRect(rect, borderPaint)
                
                currentX += columnWidth
            }
            
            currentY += cellHeight
        }
    }
    
    private fun drawText(canvas: Canvas, text: String, rect: Rect, isBold: Boolean) {
        val paint = textPaint.apply {
            isFakeBoldText = isBold
        }
        
        val textBounds = Rect()
        paint.getTextBounds(text, 0, text.length, textBounds)
        
        val x = rect.left + textPadding
        val y = rect.centerY() + textBounds.height() / 2f
        
        // 裁剪文本以适应单元格
        val availableWidth = rect.width() - textPadding * 2
        val clippedText = clipTextToWidth(text, availableWidth, paint)
        
        canvas.drawText(clippedText, x, y, paint)
    }
    
    private fun clipTextToWidth(text: String, maxWidth: Float, paint: Paint): String {
        if (paint.measureText(text) <= maxWidth) {
            return text
        }
        
        val ellipsis = "..."
        val ellipsisWidth = paint.measureText(ellipsis)
        
        var clippedText = text
        while (paint.measureText(clippedText) + ellipsisWidth > maxWidth && clippedText.isNotEmpty()) {
            clippedText = clippedText.dropLast(1)
        }
        
        return clippedText + ellipsis
    }
    
    override fun onTouchEvent(event: MotionEvent): Boolean {
        return gestureDetector.onTouchEvent(event) || super.onTouchEvent(event)
    }
    
    private fun handleTap(x: Float, y: Float) {
        val position = getCellPosition(x, y)
        if (position != null) {
            selectedRow = position.first
            selectedColumn = position.second
            invalidate()
            
            val table = reportTable ?: return
            if (position.first == -1) {
                // 点击表头
                interactionListener?.onHeaderClick(position.second, table.headers[position.second])
            } else {
                // 点击数据单元格
                val value = table.getCellData(position.first, position.second)
                interactionListener?.onCellClick(position.first, position.second, value)
            }
        }
    }
    
    private fun handleDoubleTap(x: Float, y: Float) {
        val position = getCellPosition(x, y)
        if (position != null && position.first >= 0) {
            val table = reportTable ?: return
            val value = table.getCellData(position.first, position.second)
            interactionListener?.onCellDoubleClick(position.first, position.second, value)
        }
    }
    
    private fun getCellPosition(x: Float, y: Float): Pair<Int, Int>? {
        val table = reportTable ?: return null
        
        // 确定列
        var currentX = 0f
        var columnIndex = -1
        for (i in columnWidths.indices) {
            if (x >= currentX && x < currentX + columnWidths[i]) {
                columnIndex = i
                break
            }
            currentX += columnWidths[i]
        }
        
        if (columnIndex == -1) return null
        
        // 确定行
        val rowIndex = if (y < cellHeight) {
            -1 // 表头
        } else {
            ((y - cellHeight) / cellHeight).toInt()
        }
        
        if (rowIndex >= table.data.size) return null
        
        return Pair(rowIndex, columnIndex)
    }
}
