/// 应用常量配置
class AppConstants {
  // 应用信息
  static const String appName = '一键报表生成器';
  static const String appVersion = 'v1.0';
  static const String appDescription = '智能解析项目汇报文本，自动生成结构化表格';
  
  // API配置
  static const String apiKey = "sk-c5I0FtKg8D8VScXOEWJBDja4e8ibiJkxsLI3IvGM9B6GrLmZ";
  static const String apiBaseUrl = "https://api.chatanywhere.tech/v1";
  static const String aiModel = "gpt-4.1";
  
  // 数据库配置
  static const String databaseName = 'project_reports.db';
  static const String tableName = 'saved_tables';
  static const int databaseVersion = 1;
  
  // 默认表头
  static const List<String> defaultHeaders = [
    "项目名称",
    "融资额 (亿元)",
    "项目状态",
    "核心进展",
    "下一步工作计划",
    "难点与风险",
    "最近更新日期"
  ];
  
  // UI配置
  static const double defaultCellHeight = 50.0;
  static const double minCellWidth = 120.0;
  static const double maxCellWidth = 300.0;
  static const double tablePadding = 8.0;
  
  // 颜色配置
  static const int primaryColorValue = 0xFF2196F3;
  static const int accentColorValue = 0xFF03DAC6;
  static const int errorColorValue = 0xFFB00020;
  static const int successColorValue = 0xFF4CAF50;
  static const int warningColorValue = 0xFFFF9800;
  
  // 文件导出配置
  static const String exportImageFormat = 'png';
  static const double exportImageQuality = 1.0;
  static const String exportDirectoryName = 'ReportGenerator';
  
  // 网络配置
  static const int connectTimeout = 30; // 秒
  static const int receiveTimeout = 30; // 秒
  static const int maxRetries = 3;
  
  // 示例文本
  static const String sampleText = '''1. 汈汊湖项目（融资额5亿）
✅ 核心进展： 完成住建、自规、水泊局、发改等多部门批复。
➡ 下一步工作：继续协助其他手续办理。

2. 绿美乡村项目（融资额2.6亿）
✅核心进展：调整版融资方案已经与银行、国投沟通，待确定。
➡ 下一步工作：现场继续踏勘，确保苗木绿化种植需求。 与国投确认林权证办理事宜。
‼ 难点：林地需要办理林权证。

3. 城区供水提质增效项目（融资额9.6亿）
4. 乡镇供水巩固提项目（融资额9亿）
✅核心进展：项目方案确定中。
➡ 下一步工作：编制项目建议书。

5. 全域交通配套项目（融资额4亿）
✅核心进展：可研编制中，目前因投资受限，或需调整贷款额。
    
6. 集贸片区改造项目（融资额4亿）
✅核心进展：方案编制中。''';
}
