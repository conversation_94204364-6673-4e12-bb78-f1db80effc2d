package com.reportgenerator.app.data.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.reportgenerator.app.data.model.ReportTable

/**
 * 报表数据访问对象
 */
@Dao
interface ReportTableDao {
    
    /**
     * 获取所有报表（按创建时间倒序）
     */
    @Query("SELECT * FROM saved_tables ORDER BY createdAt DESC")
    fun getAllReports(): LiveData<List<ReportTable>>
    
    /**
     * 获取所有报表（同步方法）
     */
    @Query("SELECT * FROM saved_tables ORDER BY createdAt DESC")
    suspend fun getAllReportsSync(): List<ReportTable>
    
    /**
     * 根据ID获取报表
     */
    @Query("SELECT * FROM saved_tables WHERE id = :id")
    suspend fun getReportById(id: Long): ReportTable?
    
    /**
     * 插入报表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertReport(report: ReportTable): Long
    
    /**
     * 更新报表
     */
    @Update
    suspend fun updateReport(report: ReportTable): Int
    
    /**
     * 删除报表
     */
    @Delete
    suspend fun deleteReport(report: ReportTable): Int
    
    /**
     * 根据ID删除报表
     */
    @Query("DELETE FROM saved_tables WHERE id = :id")
    suspend fun deleteReportById(id: Long): Int
    
    /**
     * 清空所有报表
     */
    @Query("DELETE FROM saved_tables")
    suspend fun clearAllReports(): Int
    
    /**
     * 获取报表数量
     */
    @Query("SELECT COUNT(*) FROM saved_tables")
    suspend fun getReportCount(): Int
    
    /**
     * 根据标题搜索报表
     */
    @Query("SELECT * FROM saved_tables WHERE title LIKE '%' || :query || '%' ORDER BY createdAt DESC")
    suspend fun searchReportsByTitle(query: String): List<ReportTable>
    
    /**
     * 获取最近的N个报表
     */
    @Query("SELECT * FROM saved_tables ORDER BY createdAt DESC LIMIT :limit")
    suspend fun getRecentReports(limit: Int): List<ReportTable>
}
