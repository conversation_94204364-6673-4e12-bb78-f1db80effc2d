package com.reportgenerator.app.di

import android.content.Context
import com.reportgenerator.app.data.dao.ReportTableDao
import com.reportgenerator.app.data.database.ReportDatabase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 数据库模块依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideReportDatabase(@ApplicationContext context: Context): ReportDatabase {
        return ReportDatabase.getDatabase(context)
    }
    
    @Provides
    fun provideReportTableDao(database: ReportDatabase): ReportTableDao {
        return database.reportTableDao()
    }
}
