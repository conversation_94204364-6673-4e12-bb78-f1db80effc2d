package com.reportgenerator.app.data.repository

import androidx.lifecycle.LiveData
import com.reportgenerator.app.data.dao.ReportTableDao
import com.reportgenerator.app.data.model.ReportTable
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 报表数据仓库
 */
@Singleton
class ReportRepository @Inject constructor(
    private val reportTableDao: ReportTableDao
) {
    
    /**
     * 获取所有报表（LiveData）
     */
    fun getAllReports(): LiveData<List<ReportTable>> {
        return reportTableDao.getAllReports()
    }
    
    /**
     * 获取所有报表（同步）
     */
    suspend fun getAllReportsSync(): List<ReportTable> {
        return reportTableDao.getAllReportsSync()
    }
    
    /**
     * 根据ID获取报表
     */
    suspend fun getReportById(id: Long): ReportTable? {
        return reportTableDao.getReportById(id)
    }
    
    /**
     * 保存报表
     */
    suspend fun saveReport(report: ReportTable): Long {
        return reportTableDao.insertReport(report)
    }
    
    /**
     * 更新报表
     */
    suspend fun updateReport(report: ReportTable): Int {
        return reportTableDao.updateReport(report)
    }
    
    /**
     * 删除报表
     */
    suspend fun deleteReport(report: ReportTable): Int {
        return reportTableDao.deleteReport(report)
    }
    
    /**
     * 根据ID删除报表
     */
    suspend fun deleteReportById(id: Long): Int {
        return reportTableDao.deleteReportById(id)
    }
    
    /**
     * 清空所有报表
     */
    suspend fun clearAllReports(): Int {
        return reportTableDao.clearAllReports()
    }
    
    /**
     * 获取报表数量
     */
    suspend fun getReportCount(): Int {
        return reportTableDao.getReportCount()
    }
    
    /**
     * 根据标题搜索报表
     */
    suspend fun searchReportsByTitle(query: String): List<ReportTable> {
        return reportTableDao.searchReportsByTitle(query)
    }
    
    /**
     * 获取最近的报表
     */
    suspend fun getRecentReports(limit: Int = 10): List<ReportTable> {
        return reportTableDao.getRecentReports(limit)
    }
}
