<resources>
    <string name="app_name">一键报表生成器</string>
    
    <!-- 主界面 -->
    <string name="history_reports">历史报表</string>
    <string name="input_text_hint">请在此粘贴项目汇报文本…\n\n示例格式：\n1. 汈汊湖项目（融资额5亿）\n✅ 核心进展：完成住建、自规、水泊局、发改等多部门批复。\n➡ 下一步工作：继续协助其他手续办理。</string>
    <string name="generate_table">一键生成表格</string>
    <string name="generating">生成中…</string>
    
    <!-- 表格操作 -->
    <string name="add_row">添加行</string>
    <string name="delete_row">删除行</string>
    <string name="add_column">添加列</string>
    <string name="delete_column">删除列</string>
    <string name="edit_header">编辑表头</string>
    <string name="edit_cell">编辑单元格</string>
    
    <!-- 文件操作 -->
    <string name="save_report">保存报表</string>
    <string name="export_image">导出图片</string>
    <string name="delete_report">删除报表</string>
    
    <!-- 对话框 -->
    <string name="dialog_save_title">保存报表</string>
    <string name="dialog_save_hint">请输入报表标题</string>
    <string name="dialog_add_column_title">添加列</string>
    <string name="dialog_add_column_hint">请输入列名</string>
    <string name="dialog_edit_header_title">编辑表头</string>
    <string name="dialog_edit_cell_title">编辑单元格</string>
    <string name="dialog_delete_confirm">确认删除</string>
    <string name="dialog_delete_message">确定要删除这个报表吗？</string>
    
    <!-- 按钮 -->
    <string name="ok">确定</string>
    <string name="cancel">取消</string>
    <string name="delete">删除</string>
    <string name="save">保存</string>
    <string name="clear">清空</string>
    <string name="paste">粘贴</string>
    
    <!-- 提示信息 -->
    <string name="empty_text_warning">请输入项目汇报文本！</string>
    <string name="save_success">报表保存成功！</string>
    <string name="export_success">图片导出成功！</string>
    <string name="delete_success">删除成功！</string>
    <string name="network_error">网络连接失败，请检查网络设置</string>
    <string name="ai_parse_error">AI解析失败，请稍后重试</string>
    <string name="permission_denied">权限被拒绝，请在设置中授予存储权限</string>
    
    <!-- 状态信息 -->
    <string name="no_history_reports">暂无历史报表</string>
    <string name="no_current_table">请输入文本生成表格或选择历史报表</string>
    <string name="table_info">%d 行 × %d 列</string>
    <string name="character_count">字符数: %d</string>
</resources>
