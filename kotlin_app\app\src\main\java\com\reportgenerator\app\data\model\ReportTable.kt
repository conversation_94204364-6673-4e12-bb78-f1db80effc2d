package com.reportgenerator.app.data.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * 报表数据模型
 */
@Entity(tableName = "saved_tables")
@TypeConverters(Converters::class)
@Parcelize
data class ReportTable(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val title: String,
    val headers: List<String>,
    val data: List<List<String>>,
    val createdAt: Date = Date()
) : Parcelable {
    
    /**
     * 获取表格行数
     */
    val rowCount: Int get() = data.size
    
    /**
     * 获取表格列数
     */
    val columnCount: Int get() = headers.size
    
    /**
     * 获取指定位置的单元格数据
     */
    fun getCellData(row: Int, column: Int): String {
        return if (row < data.size && column < data[row].size) {
            data[row][column]
        } else {
            ""
        }
    }
    
    /**
     * 更新单元格数据
     */
    fun updateCellData(row: Int, column: Int, value: String): ReportTable {
        val newData = data.toMutableList()
        
        // 确保行存在
        while (newData.size <= row) {
            newData.add(MutableList(headers.size) { "" })
        }
        
        // 确保列存在
        val rowData = newData[row].toMutableList()
        while (rowData.size <= column) {
            rowData.add("")
        }
        
        rowData[column] = value
        newData[row] = rowData
        
        return copy(data = newData)
    }
    
    /**
     * 添加新行
     */
    fun addRow(): ReportTable {
        val newRow = MutableList(headers.size) { "" }
        return copy(data = data + listOf(newRow))
    }
    
    /**
     * 删除行
     */
    fun deleteRow(index: Int): ReportTable {
        if (index < 0 || index >= data.size) return this
        val newData = data.toMutableList()
        newData.removeAt(index)
        return copy(data = newData)
    }
    
    /**
     * 添加新列
     */
    fun addColumn(headerName: String): ReportTable {
        val newHeaders = headers + headerName
        val newData = data.map { row ->
            row + ""
        }
        return copy(headers = newHeaders, data = newData)
    }
    
    /**
     * 删除列
     */
    fun deleteColumn(index: Int): ReportTable {
        if (index < 0 || index >= headers.size || headers.size <= 1) return this
        
        val newHeaders = headers.toMutableList()
        newHeaders.removeAt(index)
        
        val newData = data.map { row ->
            val newRow = row.toMutableList()
            if (newRow.size > index) {
                newRow.removeAt(index)
            }
            newRow
        }
        
        return copy(headers = newHeaders, data = newData)
    }
    
    /**
     * 更新表头
     */
    fun updateHeader(index: Int, newName: String): ReportTable {
        if (index < 0 || index >= headers.size) return this
        val newHeaders = headers.toMutableList()
        newHeaders[index] = newName
        return copy(headers = newHeaders)
    }
    
    companion object {
        /**
         * 默认表头
         */
        val DEFAULT_HEADERS = listOf(
            "项目名称",
            "融资额 (亿元)",
            "项目状态",
            "核心进展",
            "下一步工作计划",
            "难点与风险",
            "最近更新日期"
        )
        
        /**
         * 创建空报表
         */
        fun createEmpty(title: String = "新建报表"): ReportTable {
            return ReportTable(
                title = title,
                headers = DEFAULT_HEADERS,
                data = listOf(MutableList(DEFAULT_HEADERS.size) { "" })
            )
        }
    }
}

/**
 * Room 类型转换器
 */
class Converters {
    private val gson = Gson()
    
    @TypeConverter
    fun fromStringList(value: List<String>): String {
        return gson.toJson(value)
    }
    
    @TypeConverter
    fun toStringList(value: String): List<String> {
        val listType = object : TypeToken<List<String>>() {}.type
        return gson.fromJson(value, listType)
    }
    
    @TypeConverter
    fun fromStringListList(value: List<List<String>>): String {
        return gson.toJson(value)
    }
    
    @TypeConverter
    fun toStringListList(value: String): List<List<String>> {
        val listType = object : TypeToken<List<List<String>>>() {}.type
        return gson.fromJson(value, listType)
    }
    
    @TypeConverter
    fun fromDate(date: Date): Long {
        return date.time
    }
    
    @TypeConverter
    fun toDate(timestamp: Long): Date {
        return Date(timestamp)
    }
}
