import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/report_provider.dart';
import '../widgets/history_panel.dart';
import '../widgets/input_panel.dart';
import '../widgets/table_panel.dart';
import '../widgets/action_panel.dart';

/// 主界面
class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('一键报表生成器 v1.0'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<ReportProvider>().refreshHistoryReports();
            },
            tooltip: '刷新历史记录',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              context.read<ReportProvider>().createNewReport();
            },
            tooltip: '新建报表',
          ),
        ],
      ),
      body: Consumer<ReportProvider>(
        builder: (context, provider, child) {
          return Column(
            children: [
              // 错误提示
              if (provider.errorMessage != null)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  color: Colors.red.shade100,
                  child: Row(
                    children: [
                      Icon(Icons.error, color: Colors.red.shade700),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          provider.errorMessage!,
                          style: TextStyle(color: Colors.red.shade700),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: provider.clearError,
                        iconSize: 20,
                      ),
                    ],
                  ),
                ),
              
              // 主要内容区域
              Expanded(
                child: Row(
                  children: [
                    // 左侧历史面板
                    SizedBox(
                      width: 280,
                      child: HistoryPanel(),
                    ),
                    
                    // 分割线
                    Container(
                      width: 1,
                      color: Colors.grey.shade300,
                    ),
                    
                    // 右侧主要内容
                    Expanded(
                      child: Column(
                        children: [
                          // 输入面板
                          InputPanel(),
                          
                          // 分割线
                          Container(
                            height: 1,
                            color: Colors.grey.shade300,
                          ),
                          
                          // 表格面板
                          Expanded(
                            child: TablePanel(),
                          ),
                          
                          // 分割线
                          Container(
                            height: 1,
                            color: Colors.grey.shade300,
                          ),
                          
                          // 操作面板
                          ActionPanel(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
