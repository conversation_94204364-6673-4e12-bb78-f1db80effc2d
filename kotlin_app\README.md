# 一键报表生成器 Kotlin版

这是一个基于Kotlin和Android原生开发的应用，功能与原Python版本的`main.py`完全相同，能够智能解析项目汇报文本并自动生成结构化表格。

## 功能特性

### 🤖 AI智能解析
- 使用OpenAI API智能解析项目汇报文本
- 自动提取项目名称、融资额、进展状态等关键信息
- 支持多种文本格式和表达方式

### 📊 表格编辑
- 自定义表格视图，支持触摸交互
- 单击选择单元格，双击编辑内容
- 支持添加/删除行和列
- 可编辑表头名称
- 动态调整列宽和滚动支持

### 💾 数据持久化
- 使用Room数据库保存历史报表
- 支持报表的加载、保存和删除
- 自动记录创建时间
- LiveData响应式数据更新

### 🖼️ 图片导出
- 使用Android Canvas API绘制高质量表格图片
- 自动调整表格布局和字体大小
- 保存到设备Pictures目录

### 📱 现代化Android体验
- Material Design 3设计风格
- 响应式布局，适配不同屏幕尺寸
- 流畅的动画和交互效果
- 完善的权限管理

## 技术栈

- **语言**: Kotlin
- **架构**: MVVM + Repository Pattern
- **依赖注入**: Hilt
- **数据库**: Room
- **网络请求**: Retrofit + OkHttp
- **异步处理**: Coroutines + LiveData
- **UI**: Material Design Components

## 项目结构

```
app/src/main/java/com/reportgenerator/app/
├── data/                          # 数据层
│   ├── dao/                       # 数据访问对象
│   ├── database/                  # 数据库配置
│   ├── model/                     # 数据模型
│   └── repository/                # 数据仓库
├── di/                            # 依赖注入模块
├── network/                       # 网络层
│   ├── api/                       # API接口
│   ├── model/                     # 网络模型
│   └── service/                   # 网络服务
├── service/                       # 业务服务
├── ui/                            # UI层
│   ├── viewmodel/                 # ViewModel
│   └── widget/                    # 自定义控件
├── utils/                         # 工具类
└── ReportApplication.kt           # 应用程序类
```

## 安装和运行

### 前置要求

1. **Android Studio**: Arctic Fox 或更高版本
2. **Android SDK**: API 24 (Android 7.0) 或更高
3. **Kotlin**: 1.9.20 或更高版本
4. **Gradle**: 8.2.0 或更高版本

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd kotlin_app
   ```

2. **配置API密钥**
   
   在 `app/src/main/java/com/reportgenerator/app/utils/Constants.kt` 中修改API配置：
   ```kotlin
   const val API_KEY = "your-openai-api-key"
   const val API_BASE_URL = "your-api-base-url"
   ```

3. **打开项目**
   - 使用Android Studio打开项目
   - 等待Gradle同步完成

4. **运行应用**
   - 连接Android设备或启动模拟器
   - 点击"Run"按钮或使用快捷键 Ctrl+R

### 构建APK

```bash
# 构建调试版APK
./gradlew assembleDebug

# 构建发布版APK
./gradlew assembleRelease
```

生成的APK文件位于：`app/build/outputs/apk/`

## 使用说明

### 1. 文本输入
- 在输入框中粘贴或输入项目汇报文本
- 支持多种格式，如带有表情符号的进展标记
- 点击"一键生成表格"按钮进行AI解析

### 2. 表格交互
- **选择单元格**: 单击任意单元格
- **编辑单元格**: 双击单元格弹出编辑对话框
- **编辑表头**: 单击表头进行重命名
- **滚动查看**: 支持水平和垂直滚动

### 3. 表格操作
- **添加行**: 点击"添加行"按钮
- **删除行**: 点击"删除行"按钮删除最后一行
- **添加列**: 点击"添加列"按钮并输入列名
- **删除列**: 点击"删除列"按钮并选择要删除的列

### 4. 数据管理
- **保存报表**: 点击"保存报表"按钮并输入标题
- **查看历史**: 历史报表会自动显示在底部区域
- **删除历史**: 长按历史报表项目进行删除

### 5. 导出功能
- **导出图片**: 点击"导出图片"按钮
- 图片会保存到 `/Pictures/ReportGenerator/` 目录

## 权限说明

应用需要以下权限：

- **网络访问权限**: 用于调用AI API
- **存储权限**: 用于保存导出的图片文件

权限会在首次使用相关功能时自动请求。

## 核心类说明

### 数据模型
- `ReportTable`: 报表数据模型，包含表头、数据和操作方法
- `Converters`: Room类型转换器，处理复杂数据类型

### 网络层
- `OpenAIApi`: Retrofit API接口定义
- `AIService`: AI服务类，处理文本解析逻辑

### UI组件
- `MainActivity`: 主界面Activity
- `MainViewModel`: 主界面ViewModel，处理业务逻辑
- `EditableTableView`: 自定义表格视图控件

### 服务类
- `ImageExportService`: 图片导出服务
- `ReportRepository`: 数据仓库，统一数据访问

## 示例文本格式

```
1. 汈汊湖项目（融资额5亿）
✅ 核心进展： 完成住建、自规、水泊局、发改等多部门批复。
➡ 下一步工作：继续协助其他手续办理。

2. 绿美乡村项目（融资额2.6亿）
✅核心进展：调整版融资方案已经与银行、国投沟通，待确定。
➡ 下一步工作：现场继续踏勘，确保苗木绿化种植需求。
‼ 难点：林地需要办理林权证。
```

## 故障排除

### 常见问题

1. **网络连接失败**
   - 检查网络连接
   - 确认API密钥和基础URL正确
   - 检查防火墙设置

2. **权限被拒绝**
   - 在设备设置中手动授予存储权限
   - 重启应用

3. **AI解析失败**
   - 检查输入文本格式
   - 确认API配额充足
   - 查看Logcat日志

4. **编译错误**
   - 确保Android SDK和构建工具版本正确
   - 清理并重新构建项目：`./gradlew clean build`

### 调试模式

在调试模式下，应用会输出详细的日志信息，可以通过Logcat查看：

```bash
adb logcat -s ReportGenerator
```

## 性能优化

- 使用协程处理异步操作，避免阻塞UI线程
- Room数据库提供高效的本地数据存储
- 自定义表格视图优化大数据量显示
- 图片导出使用后台线程处理

## 版本历史

- **v1.0.0**: 初始版本，包含所有核心功能

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目采用MIT许可证。
