import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/report_provider.dart';
import '../services/image_export_service.dart';

/// 操作面板
class ActionPanel extends StatelessWidget {
  const ActionPanel({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Consumer<ReportProvider>(
        builder: (context, provider, child) {
          final hasReport = provider.currentReport != null;
          
          return Row(
            children: [
              // 表格操作按钮组
              _buildButtonGroup(
                title: '表格操作',
                buttons: [
                  _buildActionButton(
                    icon: Icons.add_box,
                    label: '添加行',
                    onPressed: hasReport ? () => provider.addRow() : null,
                  ),
                  _buildActionButton(
                    icon: Icons.remove_circle_outline,
                    label: '删除行',
                    onPressed: hasReport ? () => _showDeleteRowDialog(context, provider) : null,
                  ),
                  _buildActionButton(
                    icon: Icons.view_column,
                    label: '添加列',
                    onPressed: hasReport ? () => _showAddColumnDialog(context, provider) : null,
                  ),
                  _buildActionButton(
                    icon: Icons.delete_outline,
                    label: '删除列',
                    onPressed: hasReport ? () => _showDeleteColumnDialog(context, provider) : null,
                  ),
                ],
              ),
              
              const SizedBox(width: 24),
              
              // 导出操作按钮组
              _buildButtonGroup(
                title: '导出操作',
                buttons: [
                  _buildActionButton(
                    icon: Icons.save,
                    label: '保存报表',
                    onPressed: hasReport ? () => _showSaveDialog(context, provider) : null,
                  ),
                  _buildActionButton(
                    icon: Icons.image,
                    label: '导出图片',
                    onPressed: hasReport ? () => _exportAsImage(context, provider) : null,
                  ),
                ],
              ),
              
              const Spacer(),
              
              // 状态信息
              if (hasReport)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '${provider.currentReport!.data.length} 行 × ${provider.currentReport!.headers.length} 列',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  /// 构建按钮组
  Widget _buildButtonGroup({required String title, required List<Widget> buttons}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 8),
        Row(children: buttons),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
  }) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, size: 16),
        label: Text(label),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white,
          foregroundColor: onPressed != null ? Colors.blue.shade700 : Colors.grey,
          elevation: 1,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          textStyle: const TextStyle(fontSize: 12),
        ),
      ),
    );
  }

  /// 显示保存对话框
  void _showSaveDialog(BuildContext context, ReportProvider provider) {
    final controller = TextEditingController(text: provider.currentReport?.title ?? '');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('保存报表'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: '报表标题',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              final title = controller.text.trim();
              if (title.isNotEmpty) {
                await provider.saveCurrentReport(customTitle: title);
                Navigator.of(context).pop();
                
                if (provider.errorMessage == null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('报表保存成功！'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              }
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  /// 显示添加列对话框
  void _showAddColumnDialog(BuildContext context, ReportProvider provider) {
    final controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('添加列'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: '列名',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              final columnName = controller.text.trim();
              if (columnName.isNotEmpty) {
                provider.addColumn(columnName);
                Navigator.of(context).pop();
              }
            },
            child: const Text('添加'),
          ),
        ],
      ),
    );
  }

  /// 显示删除行对话框
  void _showDeleteRowDialog(BuildContext context, ReportProvider provider) {
    final report = provider.currentReport!;
    if (report.data.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('没有可删除的行')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除行'),
        content: Text('确定要删除最后一行吗？\n当前共有 ${report.data.length} 行数据。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              provider.deleteRow(report.data.length - 1);
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 显示删除列对话框
  void _showDeleteColumnDialog(BuildContext context, ReportProvider provider) {
    final report = provider.currentReport!;
    if (report.headers.length <= 1) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('至少需要保留一列')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除列'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('选择要删除的列：'),
            const SizedBox(height: 12),
            ...report.headers.asMap().entries.map((entry) {
              final index = entry.key;
              final header = entry.value;
              return ListTile(
                title: Text(header),
                leading: Radio<int>(
                  value: index,
                  groupValue: 0,
                  onChanged: (value) {
                    Navigator.of(context).pop();
                    provider.deleteColumn(index);
                  },
                ),
                dense: true,
              );
            }).toList(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  /// 导出为图片
  void _exportAsImage(BuildContext context, ReportProvider provider) async {
    try {
      final imageService = ImageExportService();
      await imageService.exportTableAsImage(provider.currentReport!);
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('图片导出成功！'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('导出失败：$e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
