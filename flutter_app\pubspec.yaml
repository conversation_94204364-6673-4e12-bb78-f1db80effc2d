name: report_generator_app
description: 报备信息自动转图表 Flutter应用

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI组件
  cupertino_icons: ^1.0.2
  
  # 数据库
  sqflite: ^2.3.0
  path: ^1.8.3
  
  # HTTP请求
  http: ^1.1.0
  dio: ^5.3.2
  
  # JSON处理
  json_annotation: ^4.8.1
  
  # 状态管理
  provider: ^6.0.5
  
  # 文件操作
  path_provider: ^2.1.1
  permission_handler: ^11.0.1
  
  # 图片处理和导出
  flutter/painting: 
  image: ^4.1.3
  
  # 文件选择和保存
  file_picker: ^6.1.1
  
  # 日期时间
  intl: ^0.18.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  flutter_lints: ^3.0.0
  json_serializable: ^6.7.1
  build_runner: ^2.4.7

flutter:
  uses-material-design: true
  
  # 字体配置
  fonts:
    - family: NotoSansCJK
      fonts:
        - asset: assets/fonts/NotoSansCJK-Regular.ttf
          weight: 400
        - asset: assets/fonts/NotoSansCJK-Bold.ttf
          weight: 700
  
  # 资源文件
  assets:
    - assets/images/
    - assets/fonts/
