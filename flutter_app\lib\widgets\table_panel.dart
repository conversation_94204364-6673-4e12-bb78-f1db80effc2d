import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/report_provider.dart';
import '../models/report_table.dart';

/// 表格显示和编辑面板
class TablePanel extends StatefulWidget {
  const TablePanel({Key? key}) : super(key: key);

  @override
  State<TablePanel> createState() => _TablePanelState();
}

class _TablePanelState extends State<TablePanel> {
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Consumer<ReportProvider>(
        builder: (context, provider, child) {
          final report = provider.currentReport;
          
          if (report == null) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.table_chart, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    '请输入文本生成表格或选择历史报表',
                    style: TextStyle(color: Colors.grey, fontSize: 16),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // 表格标题栏
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.table_view, color: Colors.blue.shade700),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        report.title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ),
                    Text(
                      '${report.data.length} 行 × ${report.headers.length} 列',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              
              // 表格内容
              Expanded(
                child: Scrollbar(
                  controller: _horizontalController,
                  scrollbarOrientation: ScrollbarOrientation.bottom,
                  child: Scrollbar(
                    controller: _verticalController,
                    child: SingleChildScrollView(
                      controller: _horizontalController,
                      scrollDirection: Axis.horizontal,
                      child: SingleChildScrollView(
                        controller: _verticalController,
                        child: _buildTable(report, provider),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// 构建表格
  Widget _buildTable(ReportTable report, ReportProvider provider) {
    const double cellHeight = 50.0;
    const double minCellWidth = 120.0;
    const double maxCellWidth = 300.0;

    // 计算每列的宽度
    final columnWidths = <double>[];
    for (int col = 0; col < report.headers.length; col++) {
      double maxWidth = report.headers[col].length * 12.0; // 表头宽度
      
      // 检查数据行的宽度
      for (int row = 0; row < report.data.length; row++) {
        if (col < report.data[row].length) {
          final cellWidth = report.data[row][col].length * 12.0;
          if (cellWidth > maxWidth) {
            maxWidth = cellWidth;
          }
        }
      }
      
      // 限制宽度范围
      maxWidth = maxWidth.clamp(minCellWidth, maxCellWidth);
      columnWidths.add(maxWidth);
    }

    return Table(
      columnWidths: Map.fromIterables(
        List.generate(report.headers.length, (i) => i),
        columnWidths.map((width) => FixedColumnWidth(width)),
      ),
      border: TableBorder.all(color: Colors.grey.shade300),
      children: [
        // 表头行
        TableRow(
          decoration: BoxDecoration(color: Colors.grey.shade100),
          children: report.headers.asMap().entries.map((entry) {
            final index = entry.key;
            final header = entry.value;
            
            return _buildHeaderCell(header, index, provider);
          }).toList(),
        ),
        
        // 数据行
        ...report.data.asMap().entries.map((rowEntry) {
          final rowIndex = rowEntry.key;
          final rowData = rowEntry.value;
          
          return TableRow(
            children: List.generate(report.headers.length, (colIndex) {
              final cellValue = colIndex < rowData.length ? rowData[colIndex] : '';
              return _buildDataCell(cellValue, rowIndex, colIndex, provider);
            }),
          );
        }).toList(),
      ],
    );
  }

  /// 构建表头单元格
  Widget _buildHeaderCell(String header, int columnIndex, ReportProvider provider) {
    return Container(
      height: 50,
      padding: const EdgeInsets.all(8),
      child: InkWell(
        onTap: () => _showEditHeaderDialog(header, columnIndex, provider),
        child: Container(
          alignment: Alignment.center,
          child: Text(
            header,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }

  /// 构建数据单元格
  Widget _buildDataCell(String value, int rowIndex, int columnIndex, ReportProvider provider) {
    return Container(
      height: 50,
      padding: const EdgeInsets.all(8),
      child: InkWell(
        onTap: () => _showEditCellDialog(value, rowIndex, columnIndex, provider),
        child: Container(
          alignment: Alignment.centerLeft,
          child: Text(
            value,
            style: const TextStyle(fontSize: 14),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }

  /// 显示编辑表头对话框
  void _showEditHeaderDialog(String currentHeader, int columnIndex, ReportProvider provider) {
    final controller = TextEditingController(text: currentHeader);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑表头'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: '表头名称',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              final newHeader = controller.text.trim();
              if (newHeader.isNotEmpty && newHeader != currentHeader) {
                provider.updateHeader(columnIndex, newHeader);
              }
              Navigator.of(context).pop();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 显示编辑单元格对话框
  void _showEditCellDialog(String currentValue, int rowIndex, int columnIndex, ReportProvider provider) {
    final controller = TextEditingController(text: currentValue);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑单元格'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: '单元格内容',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              final newValue = controller.text.trim();
              provider.updateCellData(rowIndex, columnIndex, newValue);
              Navigator.of(context).pop();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
