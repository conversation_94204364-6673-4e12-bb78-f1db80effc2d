import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/report_provider.dart';

/// 文本输入面板
class InputPanel extends StatefulWidget {
  const InputPanel({Key? key}) : super(key: key);

  @override
  State<InputPanel> createState() => _InputPanelState();
}

class _InputPanelState extends State<InputPanel> {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题和按钮行
          Row(
            children: [
              Icon(Icons.text_fields, color: Colors.blue.shade700),
              const SizedBox(width: 8),
              Text(
                '粘贴项目汇报文本:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.blue.shade700,
                ),
              ),
              const Spacer(),
              Consumer<ReportProvider>(
                builder: (context, provider, child) {
                  return ElevatedButton.icon(
                    onPressed: provider.isLoading ? null : _onGeneratePressed,
                    icon: provider.isLoading
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Icon(Icons.auto_awesome),
                    label: Text(provider.isLoading ? '生成中...' : '一键生成表格'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  );
                },
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // 文本输入框
          Container(
            height: 120,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextField(
              controller: _textController,
              focusNode: _focusNode,
              maxLines: null,
              expands: true,
              textAlignVertical: TextAlignVertical.top,
              decoration: const InputDecoration(
                hintText: '请在此粘贴项目汇报文本...\n\n示例格式：\n1. 汈汊湖项目（融资额5亿）\n✅ 核心进展：完成住建、自规、水泊局、发改等多部门批复。\n➡ 下一步工作：继续协助其他手续办理。',
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(12),
              ),
              style: const TextStyle(fontSize: 14),
            ),
          ),
          
          const SizedBox(height: 8),
          
          // 操作按钮行
          Row(
            children: [
              TextButton.icon(
                onPressed: _clearText,
                icon: const Icon(Icons.clear),
                label: const Text('清空'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.grey.shade600,
                ),
              ),
              const SizedBox(width: 8),
              TextButton.icon(
                onPressed: _pasteFromClipboard,
                icon: const Icon(Icons.content_paste),
                label: const Text('粘贴'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.blue.shade600,
                ),
              ),
              const Spacer(),
              Text(
                '字符数: ${_textController.text.length}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 生成按钮点击事件
  void _onGeneratePressed() {
    final text = _textController.text.trim();
    if (text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请输入项目汇报文本！'),
          backgroundColor: Colors.orange,
        ),
      );
      _focusNode.requestFocus();
      return;
    }

    // 调用Provider生成报表
    context.read<ReportProvider>().generateReportFromText(text);
  }

  /// 清空文本
  void _clearText() {
    _textController.clear();
    setState(() {});
  }

  /// 从剪贴板粘贴
  void _pasteFromClipboard() async {
    // 这里可以实现从剪贴板粘贴的功能
    // 由于Flutter的剪贴板API需要额外的权限配置，这里先留空
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('请手动粘贴文本'),
        duration: Duration(seconds: 1),
      ),
    );
  }
}
