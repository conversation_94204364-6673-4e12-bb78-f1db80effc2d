import 'dart:convert';
import 'package:dio/dio.dart';
import '../models/report_table.dart';
import '../utils/constants.dart';
import '../utils/logger.dart';

/// AI服务类，负责调用OpenAI API进行文本解析
class AIService {

  final Dio _dio;

  AIService() : _dio = Dio() {
    _dio.options.baseUrl = AppConstants.apiBaseUrl;
    _dio.options.headers = {
      'Authorization': 'Bearer ${AppConstants.apiKey}',
      'Content-Type': 'application/json',
    };
    _dio.options.connectTimeout = Duration(seconds: AppConstants.connectTimeout);
    _dio.options.receiveTimeout = Duration(seconds: AppConstants.receiveTimeout);
  }

  /// 将项目汇报文本转换为表格数据
  Future<ReportTable?> parseTextToTable(String reportText) async {
    if (reportText.trim().isEmpty) {
      throw Exception('输入文本不能为空');
    }

    try {
      final response = await _callAIWithRetry(reportText);
      if (response != null) {
        return _createReportTable(response);
      }
      return null;
    } catch (e) {
      throw Exception('AI解析失败: $e');
    }
  }

  /// 调用AI API，包含重试逻辑
  Future<List<Map<String, dynamic>>?> _callAIWithRetry(String text, {int maxRetries = 3}) async {
    final prompt = _buildPrompt(text);
    
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        final response = await _dio.post(
          '/chat/completions',
          data: {
            'model': _model,
            'messages': [
              {
                'role': 'system',
                'content': '你是一个擅长结构化信息提取的AI助手。请严格按照JSON格式返回数据。'
              },
              {
                'role': 'user',
                'content': prompt
              }
            ],
            'temperature': 0.2,
          },
        );

        if (response.statusCode == 200) {
          final content = response.data['choices'][0]['message']['content'];
          return _extractJsonFromResponse(content);
        } else {
          print('API错误 (尝试 $attempt/$maxRetries): ${response.statusCode} - ${response.data}');
        }
      } catch (e) {
        print('API调用异常 (尝试 $attempt/$maxRetries): $e');
        if (attempt < maxRetries) {
          await Future.delayed(Duration(seconds: attempt)); // 递增延迟
        }
      }
    }
    return null;
  }

  /// 构建AI提示词
  String _buildPrompt(String text) {
    return '''
请将以下项目汇报文本智能解析为标准表格，每个项目一行，字段包括：${_defaultHeaders.join('、')}。

要求：
1. 严格按照JSON数组格式输出，每行一个项目对象
2. 如果某个字段信息不明确，请填写"待确认"
3. 融资额请提取数字部分，单位统一为亿元
4. 最近更新日期如果文本中没有，请填写今天的日期

输入文本：
$text

请返回JSON格式：
[
  {
    "项目名称": "项目名称",
    "融资额 (亿元)": "数字",
    "项目状态": "状态",
    "核心进展": "进展描述",
    "下一步工作计划": "计划描述",
    "难点与风险": "风险描述",
    "最近更新日期": "日期"
  }
]
''';
  }

  /// 从AI响应中提取JSON数据
  List<Map<String, dynamic>>? _extractJsonFromResponse(String content) {
    try {
      // 尝试直接解析
      final decoded = json.decode(content);
      if (decoded is List) {
        return List<Map<String, dynamic>>.from(decoded);
      }
    } catch (e) {
      // 如果直接解析失败，尝试提取JSON部分
      final jsonMatch = RegExp(r'\[.*\]', dotAll: true).firstMatch(content);
      if (jsonMatch != null) {
        try {
          final decoded = json.decode(jsonMatch.group(0)!);
          if (decoded is List) {
            return List<Map<String, dynamic>>.from(decoded);
          }
        } catch (e) {
          print('JSON提取失败: $e');
        }
      }
    }
    return null;
  }

  /// 创建报表对象
  ReportTable _createReportTable(List<Map<String, dynamic>> data) {
    if (data.isEmpty) {
      throw Exception('AI返回的数据为空');
    }

    // 使用第一行数据的键作为表头
    final headers = data.first.keys.toList();
    
    // 确保表头顺序与默认表头一致
    final orderedHeaders = <String>[];
    for (final defaultHeader in _defaultHeaders) {
      if (headers.contains(defaultHeader)) {
        orderedHeaders.add(defaultHeader);
      }
    }
    // 添加其他未匹配的表头
    for (final header in headers) {
      if (!orderedHeaders.contains(header)) {
        orderedHeaders.add(header);
      }
    }

    // 转换数据为二维数组
    final tableData = data.map((row) {
      return orderedHeaders.map((header) => row[header]?.toString() ?? '').toList();
    }).toList();

    return ReportTable(
      title: '报表_${DateTime.now().toString().substring(0, 19)}',
      headers: orderedHeaders,
      data: tableData,
      createdAt: DateTime.now(),
    );
  }

  /// 释放资源
  void dispose() {
    _dio.close();
  }
}
